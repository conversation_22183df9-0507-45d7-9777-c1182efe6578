import { test, expect } from '@playwright/test';

test.describe('Shuxue.vue Component Analysis', () => {
  const testUrl = '/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123';
  
  test.beforeEach(async ({ page }) => {
    // Capture all console messages for debugging
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[CONSOLE ${type.toUpperCase()}] ${text}`);
    });
    
    // Capture JavaScript errors
    page.on('pageerror', error => {
      console.error(`[PAGE ERROR] ${error.message}`);
    });
    
    // Monitor network requests for MathJax resources
    page.on('request', request => {
      const url = request.url();
      if (url.includes('mathjax') || url.includes('tex') || url.includes('math')) {
        console.log(`[MATH RESOURCE] Loading: ${url}`);
      }
    });
    
    page.on('response', response => {
      const url = response.url();
      if (url.includes('mathjax') || url.includes('tex') || url.includes('math')) {
        console.log(`[MATH RESOURCE] Response: ${url} - Status: ${response.status()}`);
      }
    });
  });

  test('should analyze shuxue component initialization', async ({ page }) => {
    console.log('🔍 Analyzing shuxue component initialization...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Check if the shuxue component container exists
    const shuxueExists = await page.locator('.markdown-content.prose.max-w-none.tex2jax_process').isVisible();
    console.log(`📦 Shuxue component container exists: ${shuxueExists}`);
    expect(shuxueExists).toBe(true);
    
    // Analyze the component's state
    const componentState = await page.evaluate(() => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!container) return { error: 'Container not found' };
      
      return {
        hasContent: container.innerHTML.length > 0,
        contentLength: container.innerHTML.length,
        hasTexClass: container.classList.contains('tex2jax_process'),
        hasMathRendered: container.classList.contains('math-rendered'),
        childElementCount: container.children.length,
        innerHTML: container.innerHTML.substring(0, 1000) + '...'
      };
    });
    
    console.log('📊 Component State:', JSON.stringify(componentState, null, 2));
    expect(componentState.hasContent).toBe(true);
  });

  test('should verify MathJax loader integration', async ({ page }) => {
    console.log('🔧 Verifying MathJax loader integration...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Check if mathJaxLoader utility is available
    const mathJaxLoaderStatus = await page.evaluate(() => {
      // Check if the mathJaxLoader module was imported
      return {
        mathJaxGlobal: typeof window.MathJax !== 'undefined',
        mathJaxVersion: window.MathJax?.version || 'not available',
        mathJaxStartup: !!window.MathJax?.startup,
        mathJaxTypesetPromise: typeof window.MathJax?.typesetPromise === 'function',
        mathJaxConfig: window.MathJax?.config || null
      };
    });
    
    console.log('🔧 MathJax Loader Status:', JSON.stringify(mathJaxLoaderStatus, null, 2));
    expect(mathJaxLoaderStatus.mathJaxGlobal).toBe(true);
  });

  test('should test math formula processing pipeline', async ({ page }) => {
    console.log('⚙️ Testing math formula processing pipeline...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Wait for component to initialize
    await page.waitForTimeout(2000);
    
    // Analyze the math processing pipeline
    const processingAnalysis = await page.evaluate(async () => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!container) return { error: 'Container not found' };
      
      const content = container.innerHTML;
      
      // Analyze different math patterns
      const analysis = {
        // Raw math patterns before processing
        rawInlineMath: (content.match(/\$[^$\n]+\$/g) || []).length,
        rawDisplayMath: (content.match(/\$\$[\s\S]*?\$\$/g) || []).length,
        rawLatexBrackets: (content.match(/\\\[[\s\S]*?\\\]/g) || []).length,
        rawLatexParens: (content.match(/\\\([\s\S]*?\\\)/g) || []).length,
        
        // Rendered MathJax elements
        mjxContainers: container.querySelectorAll('mjx-container').length,
        mjxMath: container.querySelectorAll('mjx-math').length,
        
        // Error indicators
        mjxErrors: container.querySelectorAll('.mjx-error').length,
        
        // Processing indicators
        hasTexClass: container.classList.contains('tex2jax_process'),
        hasMathRendered: container.classList.contains('math-rendered'),
        
        // Sample content for debugging
        sampleMathExpressions: []
      };
      
      // Extract sample math expressions
      const mathMatches = content.match(/\$[^$\n]+\$|\$\$[\s\S]*?\$\$|\\\[[\s\S]*?\\\]/g);
      if (mathMatches) {
        analysis.sampleMathExpressions = mathMatches.slice(0, 5);
      }
      
      return analysis;
    });
    
    console.log('📈 Math Processing Analysis:', JSON.stringify(processingAnalysis, null, 2));
    
    if (processingAnalysis.error) {
      console.error('❌ Processing analysis failed:', processingAnalysis.error);
    } else {
      const totalMathPatterns = processingAnalysis.rawInlineMath + 
                               processingAnalysis.rawDisplayMath + 
                               processingAnalysis.rawLatexBrackets + 
                               processingAnalysis.rawLatexParens;
      
      const totalRendered = processingAnalysis.mjxContainers;
      
      console.log(`📊 Math patterns found: ${totalMathPatterns}`);
      console.log(`📊 MathJax containers rendered: ${totalRendered}`);
      
      if (totalMathPatterns > 0 && totalRendered === 0) {
        console.warn('⚠️ Math patterns found but no MathJax containers rendered!');
      }
    }
  });

  test('should analyze table rendering with math formulas', async ({ page }) => {
    console.log('📋 Analyzing table rendering with math formulas...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const tableAnalysis = await page.evaluate(() => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!container) return { error: 'Container not found' };
      
      const tables = container.querySelectorAll('table');
      const tableWrappers = container.querySelectorAll('.table-wrapper');
      const tableContainers = container.querySelectorAll('.table-container');
      
      const analysis = {
        tableCount: tables.length,
        tableWrapperCount: tableWrappers.length,
        tableContainerCount: tableContainers.length,
        tables: []
      };
      
      tables.forEach((table, index) => {
        const tableInfo = {
          index,
          hasWrapper: !!table.closest('.table-wrapper'),
          hasContainer: !!table.closest('.table-container'),
          cellCount: table.querySelectorAll('td, th').length,
          mathInCells: 0,
          renderedMathInCells: 0
        };
        
        // Count math in table cells
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
          const cellContent = cell.innerHTML;
          const mathPatterns = (cellContent.match(/\$[^$\n]+\$|\$\$[\s\S]*?\$\$/g) || []).length;
          const renderedMath = cell.querySelectorAll('mjx-container').length;
          
          tableInfo.mathInCells += mathPatterns;
          tableInfo.renderedMathInCells += renderedMath;
        });
        
        analysis.tables.push(tableInfo);
      });
      
      return analysis;
    });
    
    console.log('📋 Table Analysis:', JSON.stringify(tableAnalysis, null, 2));
    
    if (tableAnalysis.error) {
      console.error('❌ Table analysis failed:', tableAnalysis.error);
    } else {
      console.log(`📊 Found ${tableAnalysis.tableCount} tables`);
      tableAnalysis.tables.forEach((table, index) => {
        console.log(`  Table ${index}: ${table.mathInCells} math patterns, ${table.renderedMathInCells} rendered`);
      });
    }
  });

  test('should test MathJax re-rendering functionality', async ({ page }) => {
    console.log('🔄 Testing MathJax re-rendering functionality...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Test manual re-rendering
    const reRenderResult = await page.evaluate(async () => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!container) return { error: 'Container not found' };
      
      if (!window.MathJax || !window.MathJax.typesetPromise) {
        return { error: 'MathJax typesetPromise not available' };
      }
      
      try {
        // Clear previous rendering
        if (window.MathJax.typesetClear) {
          window.MathJax.typesetClear([container]);
        }
        
        // Force re-render
        await window.MathJax.typesetPromise([container]);
        
        return {
          success: true,
          renderedElements: container.querySelectorAll('mjx-container').length,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('🔄 Re-render Result:', JSON.stringify(reRenderResult, null, 2));
    
    if (reRenderResult.error) {
      console.error('❌ Re-rendering failed:', reRenderResult.error);
    } else {
      console.log(`✅ Re-rendering successful: ${reRenderResult.renderedElements} elements rendered`);
      expect(reRenderResult.success).toBe(true);
    }
  });

  test('should capture detailed debugging information', async ({ page }) => {
    console.log('🐛 Capturing detailed debugging information...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Capture comprehensive debugging info
    const debugInfo = await page.evaluate(() => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      
      return {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        mathJax: {
          loaded: typeof window.MathJax !== 'undefined',
          version: window.MathJax?.version,
          startup: !!window.MathJax?.startup,
          config: window.MathJax?.config
        },
        container: container ? {
          exists: true,
          classes: container.className,
          contentLength: container.innerHTML.length,
          childCount: container.children.length,
          hasContent: container.innerHTML.length > 0
        } : { exists: false },
        performance: {
          loadTime: performance.now(),
          timing: performance.timing
        },
        errors: window.console?.errors || []
      };
    });
    
    console.log('🐛 Debug Information:', JSON.stringify(debugInfo, null, 2));
    
    // Save debug info to file
    await page.evaluate((info) => {
      const blob = new Blob([JSON.stringify(info, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'math-debug-info.json';
      a.click();
      URL.revokeObjectURL(url);
    }, debugInfo);
    
    expect(debugInfo.container.exists).toBe(true);
  });
});
