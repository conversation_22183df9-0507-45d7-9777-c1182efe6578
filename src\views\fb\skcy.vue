<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="left">
      <div class="lb" style=""></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
      <a-button @click="left">p</a-button>
      <a-input-number id="inputNumber" v-model:value="current_page" :min="1" @change="getData" />
      <a-button @click="right">n</a-button>
      <a-collapse v-model:active-key="activeKey" accordion>
        <template v-for="(cy, index) in data" :key="index">
          <a-collapse-panel @click="getTimu(cy.timus, cy.name)">
            <template #header>
              <span style="color: red; font-size: 18px"
                >{{ (current_page - 1) * perpage + index + 1 }}.{{ cy.name }}</span
              >
              <p style="color: black; font-size: 18px">{{ cy.jieshi }} {{ cy.count }}</p>
            </template>
            <a-textarea
              v-if="!cy.jieshi"
              v-model:value="jieshitext"
              placeholder="Autosize height based on content lines"
              auto-size
              @change="postjieshi(jieshitext, cy.id)"
            />
            <div v-for="(item, index) in timusdata">
              <div
                class="maintimu"
                style="color: black; font-size: 18px"
                v-html="item.content"
              ></div>
              <div class="item" style="color: black; font-size: 18px">
                <p
                  class="an_a"
                  :style="{
                    color: item.answer === 'A' ? 'red' : '',
                  }"
                  v-html="item.A"
                ></p>
                <p
                  class="an_b"
                  :style="{ color: item.answer === 'B' ? 'red' : '' }"
                  v-html="item.B"
                ></p>
                <p
                  class="an_c"
                  :style="{ color: item.answer === 'C' ? 'red' : '' }"
                  v-html="item.C"
                ></p>
                <p
                  class="an_d"
                  :style="{ color: item.answer === 'D' ? 'red' : '' }"
                  v-html="item.D"
                ></p>
              </div>
              <p>================================</p>
              <div class="answer" style="color: red; font-size: 18px">
                <div class="ansinfo">{{ item.source }}{{ item.createdTime }}</div>
                <br />
                <div>
                  <div v-html="item.solution"></div>
                </div>
                <div></div>
                <p>==================</p>
              </div>
            </div>
          </a-collapse-panel>
        </template>
      </a-collapse>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="right">
      <div class="rb" style=""></div>
    </a-col>
  </a-row>
</template>
<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  const activeKey = ref([]);
  const text = ref('wcnm');
  const data = ref([]);
  const timusdata = ref([]);
  const jieshitext = ref('');
  const current_page = ref(1);
  const perpage = ref(30);
  const postjieshi = async (text, id) => {
    const url = '/egg/fbgetmncy';
    text = `【解释】` + text;
    let params = {
      biao: 'fbskcy1',
      text: text,
      id: id,
      jiexi: '2',
    };
    try {
      const response = await axios.get(url, { params });
      console.log(response.data);
      console.log(text, id);
      jieshitext.value = null;
      await getData();
    } catch (error) {
      console.error(error);
    }
  };
  const open_count = ref(0);

  const updateValue = async (delta) => {
    open_count.value = 0;
    const newValue = +current_page.value + delta;
    if (newValue >= 1 && newValue <= 100000) {
      current_page.value = newValue;
      await getData();
    }
  };

  const right = async () => {
    await updateValue(1);
  };

  const left = async () => {
    await updateValue(-1);
  };
  const getTimu = async (timus, name) => {
    const url = '/egg/fbgetmncy';
    let params = {
      biao: 'fbgwy',
      type: 'skkj',
      timus: timus,
    };
    let x = [];
    try {
      const response = await axios.get(url, { params });
      timusdata.value = response.data;
      let i = 0;
      for (let item of response.data) {
        if (item.answerone.match(name) && item.answer === 'A') {
          x.push(item);
          i++;
        } else if (item.answertwo.match(name) && item.answer === 'B') {
          x.push(item);
          i++;
        } else if (item.answerthree.match(name) && item.answer === 'C') {
          x.push(item);
          i++;
        } else if (item.answerfour.match(name) && item.answer === 'D') {
          x.push(item);
          i++;
        }
      }
      if (i === 0) {
        timusdata.value = response.data;
      } else {
        timusdata.value = x;
      }
    } catch (error) {
      console.error(error);
    }
  };
  const getData = async () => {
    const url = '/egg/fbgetmncy';
    let params = {
      biao: 'fbskcy1',
      per: perpage.value,
      page: current_page.value,
    };
    console.log(params);
    try {
      const response = await axios.get(url, { params });
      data.value = response.data;
      console.log(data.value);
    } catch (error) {
      console.error(error);
    }
  };
  onMounted(async () => {
    await getData();
  });
</script>

<style scoped></style>
