<script>
  export default {
    name: 'Air',
    data: function () {
      return {
        airdata: [],
      };
    },
    mounted() {
      this.getData();
    },
    methods: {
      async getData() {
        const list = ['lylzxm', 'lyhfxm'];
        let alldata = [];

        // 使用Promise.all等待所有请求完成
        await Promise.all(
          list.map(async (item) => {
            const res = await axios.get('/egg/' + item);
            alldata.push(res.data);
          }),
        );

        this.airdata = alldata;
      },
    },
  };
</script>

<template>
  <a-row>
    <!--div居中 -->
    <div v-for="(item, index) in airdata" key="index" class="card">
      <a-col :span="24">
        <a-card title="Card" :bordered="false" style="width: 300px">
          <p>
            {{ item[0].originAirportShortName }}
            {{ item[0].arriveAirportShortName }}
          </p>
          <div v-for="(obj, indexx) in item" :key="indexx">
            <p>{{ obj.airCompanyName }} {{ obj.flightNo }}</p>
            <p>{{ obj.flyOffTime }}</p>
            <p>{{ obj.arrivalTime }}</p>
            <div v-for="(prices, j) in obj.productPrices" :key="j" class="font-bold">
              <p>{{ prices + 160 }}</p>
            </div>
            <br />
          </div>
        </a-card>
      </a-col>
      <br />
    </div>
  </a-row>
</template>
<style scoped>
  .card {
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
