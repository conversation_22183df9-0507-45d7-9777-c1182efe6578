# 开发需求指南模板

## 📋 需求基本信息

### 需求标题
**简洁描述功能名称**

### 需求类型
- [ ] 新功能开发
- [ ] 功能优化/改进
- [ ] Bug 修复
- [ ] 性能优化
- [ ] 安全加固
- [ ] 代码重构
- [ ] 文档更新

### 优先级
- [ ] 紧急 (P0)
- [ ] 高 (P1)
- [ ] 中 (P2)
- [ ] 低 (P3)

### 预估工期
**预计开发时间：** ___ 天

---

## 🎯 需求详细描述

### 业务背景
**描述为什么需要这个功能，解决什么问题**

### 功能描述
**详细描述要实现的功能**

### 用户故事
**作为 [用户角色]，我希望 [功能描述]，以便 [价值/目标]**

---

## 🏗️ 技术实现要求

### 涉及的技术栈
- [ ] Egg.js 后端
- [ ] Vue 3 前端
- [ ] MySQL 数据库
- [ ] Redis 缓存
- [ ] Socket.IO 实时通信
- [ ] 其他：___________

### 涉及的模块/文件
**请列出需要修改或新增的文件路径**
- 后端：
  - [ ] `app/controller/`
  - [ ] `app/service/`
  - [ ] `app/router.js`
  - [ ] `config/`
  - [ ] 其他：___________

- 前端：
  - [ ] `vue/src/views/`
  - [ ] `vue/src/components/`
  - [ ] `vue/src/store/`
  - [ ] `vue/src/router/`
  - [ ] 其他：___________

### API 接口设计
**如果涉及新的 API 接口，请描述**

#### 接口 1
- **路径：** `GET/POST /api/xxx`
- **参数：**
  ```json
  {
    "param1": "string",
    "param2": "number"
  }
  ```
- **返回：**
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {}
  }
  ```

### 数据库设计
**如果涉及数据库变更，请描述**

#### 新增表
```sql
CREATE TABLE `table_name` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field1` varchar(255) NOT NULL COMMENT '字段1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表说明';
```

#### 修改表
```sql
ALTER TABLE `existing_table` ADD COLUMN `new_field` varchar(255) DEFAULT NULL COMMENT '新字段';
```

---

## 🎨 UI/UX 要求

### 页面设计
**描述页面布局、交互方式**

### 响应式要求
- [ ] 桌面端适配
- [ ] 移动端适配
- [ ] 平板适配

### 主题支持
- [ ] 支持当前主题系统
- [ ] 需要新增主题样式

---

## ✅ 验收标准

### 功能验收
- [ ] 功能按需求正常工作
- [ ] 所有边界情况处理正确
- [ ] 错误处理完善

### 性能要求
- [ ] 页面加载时间 < 2秒
- [ ] API 响应时间 < 500ms
- [ ] 内存使用合理

### 代码质量
- [ ] 代码符合项目规范
- [ ] 包含完整的错误处理
- [ ] 包含必要的注释
- [ ] 通过 ESLint 检查

### 测试要求
- [ ] 单元测试覆盖
- [ ] 集成测试通过
- [ ] 手动测试通过

---

## 🔒 安全考虑

### 数据安全
- [ ] 输入参数验证
- [ ] SQL 注入防护
- [ ] XSS 攻击防护

### 权限控制
- [ ] 用户身份验证
- [ ] 操作权限检查
- [ ] 数据访问控制

---

## 📚 相关资源

### 参考文档
- [相关文档链接]

### 设计稿
- [设计稿链接或文件路径]

### 相关需求
- [关联的其他需求]

---

## 💡 备注说明

### 特殊要求
**任何特殊的技术要求或限制**

### 风险点
**可能遇到的技术难点或风险**

### 后续规划
**这个功能的后续迭代计划**

---

## 📝 需求确认

### 需求提出人
**姓名：** ___________  
**日期：** ___________

### 开发负责人
**姓名：** ___________  
**预计完成时间：** ___________

---

## 使用说明

1. **复制此模板** 创建新的需求文档
2. **填写相关信息** 根据实际需求填写各个部分
3. **删除不需要的部分** 保持文档简洁
4. **与开发人员确认** 确保需求理解一致

### 示例需求文件命名
- `需求-用户登录功能-20250125.md`
- `需求-数据导出优化-20250125.md`
- `需求-实时通知系统-20250125.md`
