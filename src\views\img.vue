<template>
  <div></div>
</template>

<script>
  import { useRoute } from 'vue-router';
  export default {
    name: 'MyImage', // Change the component name to something unique
    setup() {
      const route = useRoute();
      const body = route.query.body;
      console.log(body);
      const getData = () => {
        const f = route.query.f;
        const s = route.query.s;
        console.log(f, s);
      };

      return {
        getData,
      };
    },
  };
</script>

<style scoped></style>
