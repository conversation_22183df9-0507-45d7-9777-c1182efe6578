<script setup>
  import StoreOne from '../components/storeone.vue';
  import StoreTwo from '../components/storetwo.vue';
</script>

<template>
  <div>
    <el-row>
      <el-col :span="12"
        ><div class="grid-content ep-bg-purple" />
        <StoreOne
      /></el-col>
      <el-col :span="12"
        ><div class="grid-content ep-bg-purple-light" />
        <StoreTwo
      /></el-col>
    </el-row>
  </div>
</template>

<style scoped></style>
