<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="lb" style="" @click="left"></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <div v-show="showTop">
        <span class="top-input" style="position: fixed; top: 0; z-index: 99">
          <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
          <a-button @click="prev_page">p</a-button>
          <a-input-number
            id="inputNumber"
            v-model:value="current_page"
            :min="1"
            @change="getData"
          />
          <a-button @click="next_page">n</a-button>
          <a-button @click="allshow">all</a-button>
          <a-button @click="toggleBackground">背景</a-button>
          <div
            v-show="showCanvas"
            style="
              background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
              position: fixed;
              top: 0;
              position: relative;
            "
          >
            <div class="top-button">
              <a-button @click="toggleCanvas">画布1</a-button>
              <a-button @click="initCanvas">init画布</a-button>
            </div>
          </div>
        </span>
      </div>
      <div class="wp">
        <canvas
          v-show="showCanvas"
          id="drawingCanvas"
          style="position: absolute; background-color: rgba(255, 255, 255, 0)"
          @mousedown="startDrawing"
          @mouseup="stopDrawing"
          @mousemove="draw"
          @mouseleave="stopDrawing"
          @touchstart="startDrawingTouch"
          @touchmove="drawTouch"
          @touchend="stopDrawing"
        ></canvas>
        <div v-html="zltimu.content"></div>

        <a-row>
          <a-col v-for="(item, index) in data" :key="index" :span="24" class="datalist">
            <div v-if="isLoading">Loading...</div>
            <div v-if="!isLoading" :class="index === 1 ? 'items' : ''">
              <!--              <div>{{ resolution }}</div>-->
              <p
                v-if="
                  (item.type === 201 && (index === 0 || index % 21 === 0)) ||
                  (item.type === 203 && (index === 0 || index % 8 === 0)) ||
                  (item.type === 204 && (index === 0 || index % 2 === 0))
                "
                class="kjname"
                v-html="item.name"
              ></p>
              <!--              <spna-->
              <!--                ><a-button @click="insertdata(item.oid, item.id)"-->
              <!--                  >插入</a-button-->
              <!--                ></spna-->
              <!--              >-->
              <div
                v-if="index % 8 >= 4 && index % 8 <= 7"
                class="maintimu"
                v-html="item.content"
              ></div>

              <!-- <fbtagVVV
                  :messageFromParent="item.id"
                  :messageFromParent1="typex"
                ></fbtag> -->
              <div v-if="showtimu" class="item">
                <p class="an_a" v-html="item.A"></p>
                <p class="an_b" v-html="item.B"></p>
                <p class="an_c" v-html="item.C"></p>
                <p class="an_d" v-html="item.D"></p>
              </div>
              <!--              <p @click="toggleTop">================</p>-->
              <div v-show="showContent" v-if="index % 8 >= 4 && index % 8 <= 7" class="answer">
                <div v-if="ansinfo" class="ansinfo">{{ item.source }}{{ item.createdTime }}</div>
                <br />
                <div v-if="daanblock">
                  <div :style="showdaanblock" v-html="item.solution"></div>
                </div>
                <div></div>
                <p>==================</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-show="showTop" class="top">
        <span class="top-input">
          <!--          <a-input-number-->
          <!--            id="inputNumber"-->
          <!--            v-model:value="perpage"-->
          <!--            @change="getData"-->
          <!--            :min="1"-->
          <!--          />-->
          <!--          <a-button @click="prev_page">p</a-button>-->
          <!--          <a-input-number-->
          <!--            id="inputNumber"-->
          <!--            v-model:value="current_page"-->
          <!--            @change="getData"-->
          <!--            :min="1"-->
          <!--          />-->
          <!--          <a-button @click="next_page">n</a-button>-->
          <a-button @click="toggledaanblock">隐藏答案块</a-button>
          <a-button @click="toggleshowdaan">
            {{ showdaan === true ? '已' : '不' }}显示答案{{ total }}
          </a-button>
          <!--          <a-button @click="recoverpage">恢复页数</a-button>-->
          <a-button class="showans" @click="togglean">显示答案</a-button>
          <a-button @click="toggleansblock">隐藏答案</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <!--          <fbhiddentag></fbhiddentag>-->
          <a-button @click="toggleCanvas">画布</a-button>
          <a-button @click="initCanvas">init画布</a-button>
          <!-- <a-button @click="savepage">保存页数</a-button> -->
          <a-button @click="showvideo = !showvideo">{{
            showvideo === true ? '隐藏视频' : '显示视频'
          }}</a-button>
          <a-button @click="zuotivideo = !zuotivideo">{{
            zuotivideo === false ? '开视频' : '关视频'
          }}</a-button>
        </span>
        <div class="top-button"></div>
      </div>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="rb" style="" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';
  import DPlayer from 'dplayer';
  import dp from '../../components/dp.vue';

  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current_page = ref(1);
  const showContent = ref(false);
  const perpage = ref(1);
  const total = ref(0);
  const route = useRoute();
  const isTagHidden = ref(false);
  const showdaan = ref(false);
  const showdaanblock = ref('');
  const resolution = ref('');
  const open_count = ref(0);
  const drawing = ref(false);
  const showCanvas = ref(false);
  const ctx = ref(null);
  const daanblock = ref(true);
  const isBackgroundActive = ref(true);
  const showvideo = ref(true);
  const showtimu = ref(true);
  const zuotivideo = ref(false);
  const ansinfo = ref(false);
  const currenttype = ref(0);
  const allshow = async () => {
    current_page.value = 1;
    perpage.value = +total.value;
    await getData();
  };
  const getResolution = () => {
    resolution.value = `${window.innerWidth} x ${window.innerHeight}`;
  };
  const toggleHiddenTag = () => {
    // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
    const elements = document.querySelectorAll('.tag');

    // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
    elements.forEach((element) => {
      if (isTagHidden.value) {
        element.style.display = 'block'; // 或者其他适当的显示样式
      } else {
        element.style.display = 'none';
      }
    });

    // 切换状态变量
    isTagHidden.value = !isTagHidden.value;
  };

  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const toggledaanblock = () => {
    daanblock.value = !daanblock.value;
  };

  const prev_page = () => {
    left();
  };
  const next_page = () => {
    right();
  };

  const toggleshowdaan = async () => {
    showdaan.value = !showdaan.value;
    await initCanvas();
  };

  const toggleCanvas = async () => {
    showCanvas.value = !showCanvas.value;
  };
  const toggleansblock = async () => {
    showContent.value = !showContent.value;
    await ansblack();
  };
  const handleKeyDown = async (event) => {
    if (event.key === '0' || event.key === 'q') {
      await left();
    } else if (event.key === '.' || event.key === 'e') {
      await right();
    } else if (event.key === 'h') {
      const showAnsButton = document.querySelector('.an_a');
      await showAnsButton.click();
    } else if (event.key === 'j') {
      const showAnsButton = document.querySelector('.an_b');
      await showAnsButton.click();
    } else if (event.key === 'k') {
      const showAnsButton = document.querySelector('.an_c');
      await showAnsButton.click();
    } else if (event.key === 'l') {
      const showAnsButton = document.querySelector('.an_d');
      await showAnsButton.click();
    } else if (event.key === 'u') {
      await toggleCanvas();
    } else if (event.key === 'i') {
      await initCanvas();
    } else if (event.key === 's') {
      await savepage();
    } else if (event.key === 'r') {
      await recoverpage();
    } else if (event.key === '1') {
      await pushans(data.value[0].id, 'A');
    } else if (event.key === '2') {
      await pushans(data.value[0].id, 'B');
    } else if (event.key === '3') {
      await pushans(data.value[0].id, 'C');
    } else if (event.key === '4') {
      await pushans(data.value[0].id, 'D');
    }
  };

  const updateValue = async (delta) => {
    open_count.value = 0;
    const newValue = +current_page.value + delta;
    if (newValue >= 1 && newValue <= 100000) {
      current_page.value = newValue;
      await getData();
      if (!showdaan.value) {
        showContent.value = false;
        await ansblack();
      } else {
        showContent.value = true;
        await ansred();
      }
    }
  };

  const right = async () => {
    await updateValue(1);
  };

  const left = async () => {
    await updateValue(-1);
  };

  const toggleContent = async (event) => {
    if (
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown' ||
      event.key === 'w' ||
      event.key === 'x'
    ) {
      await getData();
      if (showContent.value) {
        await ansblack();
        showContent.value = false;
      } else {
        await ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = async () => {
    if (+currenttype.value === 201) {
      await ansblack();
      for (let i = 0; i < data.value.length; i++) {
        // console.log(data.value);
        if (data.value && data.value[i].choice === 'A') {
          document.getElementsByClassName('an_a')[i].style.color = 'red';
        }
        if (data.value && data.value[i].choice === 'B') {
          document.getElementsByClassName('an_b')[i].style.color = 'red';
        }
        if (data.value && data.value[i].choice === 'C') {
          document.getElementsByClassName('an_c')[i].style.color = 'red';
        }
        if (data.value && data.value[i].choice === 'D') {
          document.getElementsByClassName('an_d')[i].style.color = 'red';
        }
        if (data.value && data.value[i].answer === 'A') {
          document.getElementsByClassName('an_a')[i].style.color = 'red';
        }
        if (data.value && data.value[i].answer === 'B') {
          document.getElementsByClassName('an_b')[i].style.color = 'red';
        }
        if (data.value && data.value[i].answer === 'C') {
          document.getElementsByClassName('an_c')[i].style.color = 'red';
        }
        if (data.value && data.value[i].answer === 'D') {
          document.getElementsByClassName('an_d')[i].style.color = 'red';
        }
      }
    }
  };

  const ansblack = async () => {
    if (+currenttype.value === 201) {
      for (let i = 0; i < data.value.length; i++) {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
        document.getElementsByClassName('an_b')[i].style.color = 'black';
        document.getElementsByClassName('an_c')[i].style.color = 'black';
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = async (answer) => {
    if (showContent.value && !showdaan.value && open_count.value === 0) {
      await ansblack();
      showContent.value = false;
    } else {
      open_count.value++;
      await ansred();
      showContent.value = true;
    }
    // console.log('打开次数', open_count.value);
  };
  const recoverpage = async () => {
    const url = '/egg/fbrecoverpage';
    const id = route.query.id || '222222' + currenttype.value;
    const b = route.query.b || 0;
    if (b === 0) {
      let params = {
        id: +id,
      };
      const response = await axios.get(url, { params });
      current_page.value = response.data.page || current_page.value;
      console.log(current_page.value);
    }
    await getData();
    await ansblack();
  };
  const savepage = async () => {
    const url = '/egg/fbremeber';
    const type = route.query.type || 'sy';
    const id = route.query.id || '222222' + currenttype.value;
    const b = route.query.b || 0;
    if (b === 0) {
      const response = await axios.post(url, {
        id: +id,
        page: current_page.value,
      });
      if (+response.data.affectedRows !== 0) {
        console.log(`记住${current_page.value}成功`);
        message.success({
          content: `记住${current_page.value}成功`,
          duration: 6,
          style: {
            marginTop: '80vh',
          },
        });
      }
    }
  };
  const insertdata = async (id, sort) => {
    const url = '/egg/fbupdatezlfx5000';
    let params = {
      id: id,
      sort: sort,
      biao: 'yanyu5000',
    };
    console.log(params);

    try {
      const response = await axios.get(url, { params });
      console.log(response);
      if (response.status === 200) {
        if (response.data.code === 0) {
          message.success({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '80vh',
            },
          });
        } else if (response.data.code === 1) {
          message.error({
            content: response.data.message,
            duration: 1,
            style: {
              marginTop: '60vh',
            },
          });
        }
      }
    } catch (error) {
      message.error(error.response.data.message);
    }
  };
  const pushans = async (id, answer) => {
    const url = '/egg/fbchoice';
    const type = route.query.type || 'sy';
    console.log(type);
    const response = await axios.post(url, {
      id: id,
      choice: answer,
      type: type,
    });
    // console.log(response);
    if (response.data.affectedRows !== 0) {
      message.success({
        content: `选择${answer}成功`,
        style: {
          marginTop: '80vh',
        },
      });
      await getData();
      await ansred();
      await savepage();
      showContent.value = true;
    }
  };
  const initCanvas = async () => {
    const canvas = document.getElementById('drawingCanvas');
    canvas.width = document.querySelector('.wp').offsetWidth;
    canvas.height = document.querySelector('body').offsetHeight;
    ctx.value = canvas.getContext('2d');
  };

  const startDrawing = (event) => {
    drawing.value = true;
    draw(event);
  };

  const stopDrawing = () => {
    drawing.value = false;
    ctx.value.beginPath();
  };

  const draw = (event) => {
    if (!drawing.value) return;
    ctx.value.strokeStyle = 'blue';
    ctx.value.lineWidth = 3;
    ctx.value.lineCap = 'round';
    ctx.value.lineTo(event.offsetX, event.offsetY);
    ctx.value.stroke();
    ctx.value.beginPath();
    ctx.value.moveTo(event.offsetX, event.offsetY);
  };

  const startDrawingTouch = (event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
    const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
    startDrawing({
      offsetX,
      offsetY,
    });
  };

  const drawTouch = (event) => {
    event.preventDefault();
    const touch = event.touches[0];
    const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
    const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
    draw({
      offsetX,
      offsetY,
    });
  };

  const toggleBackground = async () => {
    if (isBackgroundActive.value) {
      document.body.style.background = '#fcf2d7 url(/bg_paper_mid.jpg)';
    } else {
      document.body.style.background = '';
    }
    isBackgroundActive.value = !isBackgroundActive.value;
  };

  const getData = async () => {
    perpage.value = +perpage.value;
    const type = route.query.type || '201,203';
    const per = route.query.per || perpage.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const z = route.query.z || 1;
    const b = route.query.b || 0;
    const t = route.query.t || 0;
    const d = route.query.d || 0;
    const q = route.query.q || '1=1';
    const page = route.query.page || current_page.value;
    currenttype.value = +route.query.type;
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/jskaojuanshumu';
    let params = {
      per: per,
      page: page,
      id: id,
      type: type,
      z: z,
      b: b,
      q: q,
      t: !t ? 0 : t,
    };
    // console.log(params);
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   console.log('z1', z);
      //   await getData();
      //   return;
      // }
      await savepage();
      if (a) {
        showContent.value = true;
      }
      // console.log(response.data.pagetotal[0].total);
      data.value = response.data;
      let i = 1;
      console.log(data.value.length);

      for (let item in data.value) {
        data.value[item].A = 'A.' + data.value[item].A;
        data.value[item].B = 'B.' + data.value[item].B;
        data.value[item].C = 'C.' + data.value[item].C;
        data.value[item].D = 'D.' + data.value[item].D;
        data.value[item].content = data.value[item].content.replace(/<p>/, '<p>' + i + '.');
        if (type === '201') {
          data.value[item].solution = data.value[item].solution.replace(
            /<p>/,
            '<p><span style="color:blue;">' +
              (+data.value[item].choice === 0
                ? 'A'
                : +data.value[item].choice === 1
                  ? 'B'
                  : +data.value[item].choice === 2
                    ? 'C'
                    : 'D') +
              '</span>.',
          );
        }

        data.value[item].choice =
          +data.value[item].choice === 0
            ? 'A'
            : +data.value[item].choice === 1
              ? 'B'
              : +data.value[item].choice === 2
                ? 'C'
                : 'D';
        i++;
        if (i % 22 === 0 && type === '201') {
          i = 1;
        }
        if (i % 9 === 0 && type === '203') {
          i = 1;
        }
        if (i % 3 === 0 && type === '204') {
          i = 1;
        }
      }
      if (+z && type === '201') {
        function formatString(input) {
          let formatted = '';
          for (let i = 0; i < input.length; i++) {
            formatted += input[i];
            if ((i + 1) % 5 === 0 && i + 1 !== input.length) {
              formatted += ' ';
            }
          }
          return formatted;
        }

        let anslist = [];
        let text = '';
        let i = 0; // Start from 0 to match array indexing
        let j = 0;

        for (let item in data.value) {
          text += data.value[item].choice;

          if ((i + 1) % 21 === 0) {
            const formattedString = formatString(text);
            anslist.push(formattedString);

            // console.log(j + 1);
            // console.log(formattedString);

            data.value[j * 21].name += `<br>` + formattedString;

            text = '';
            j++;
          }
          i++;
        }
      }

      if (+b === 1) {
        showtimu.value = false;
        console.log(showtimu.value);
      }
      if (+d === 1) {
        let x = [];
        let i = 1;
        for (let item of data.value) {
          // console.log(item.id);
          if (!item.content.match('原则') && i % 2 !== 0) {
            x.push(item);
            console.log(i);
          }
          i++;
        }
        data.value = x;
      } else if (+d === 2) {
        let x = [];
        let i = 1;
        for (let item of data.value) {
          // console.log(item.id);
          if (i % 2 === 0) {
            x.push(item);
            console.log(i);
          }
          i++;
        }
        data.value = x;
      }
      if (q !== '1=1') {
        ansinfo.value = true;
      }
      if (+t === 1) {
        console.log(1, ansinfo.value);
        const res = await axios.get(`/egg/jscuo`);
        console.log(res.data);
        let x = [];
        for (let item of data.value) {
          for (let resitem of res.data) {
            if (+item.id === +resitem.sort) {
              x.push(item);
            }
          }
        }
        data.value = x;
      }
      // ansblack();
      // showContent.value = false;
      isLoading.value = false;
      await initCanvas();
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(async () => {
    currenttype.value = +route.query.type;
    document.addEventListener('keydown', toggleContent);
    await toggleBackground();
    await recoverpage();
    window.addEventListener('keydown', handleKeyDown);
    getResolution();
    await initCanvas();
    window.addEventListener('resize', getResolution);
    window.addEventListener('resize', initCanvas);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
    window.removeEventListener('resize', getResolution);
    window.removeEventListener('resize', initCanvas);
  });
</script>

<style>
  /* body {
  background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
} */
  * {
    margin: 0;
    padding: 0;
    font-size: 20px;
  }
  button {
    line-height: 0 !important;
  }

  .top {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    position: fixed;
    bottom: 0;
  }
  img {
    max-width: 100%;
    max-height: 100%;
  }
  .top {
    display: flex;
    flex-direction: column;
  }

  .top-button,
  .top-input {
    display: flex;
    flex-wrap: wrap;

    gap: 10px; /* Adjust the gap as needed */
  }
  .wp {
    color: black;
    height: 100%;
    width: 100%; /* 宽度占满父容器 */
    max-width: 100vw; /* 最大宽度不超过视口宽度 */
    word-wrap: break-word; /* 在单词内部进行换行 */
    overflow-x: hidden;
    padding-top: 33px;
    padding-bottom: 62px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }
  @media only screen and (max-width: 1024px) {
    * {
      margin: 0;
      padding: 0;
      font-size: 24px;
    }
  }
  @media only screen and (max-width: 576px) {
    * {
      font-size: 20px;
    }
    :where(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    :where(.css-dev-only-do-not-override-hkh161).ant-pagination {
      width: 100%;
    }
    .rb {
      background-color: rgba(240, 240, 240, 0.3);
    }
    :global(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    .top-button {
      display: block;
    }
    .top-input {
      display: block;
    }
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  canvas {
    border: 1px solid black;
  }
  #drawingCanvas {
    z-index: 10; /* 确保画布在内容之上 */
    max-width: 100vw; /* 确保画布宽度不超过视口宽度 */
  }
</style>
