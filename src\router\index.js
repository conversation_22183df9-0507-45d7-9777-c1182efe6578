import { createRouter, createWebHistory } from 'vue-router';

/**
 * 从 /src/views/ 目录动态生成路由。
 * 此优化版本使用单个 glob 模式和更清晰的逻辑来生成路由，
 * 避免了原有多重循环和可能重复的路由。
 *
 * 路由生成规则:
 * - `../views/Page.vue`      -> path: `/page`, name: `page`
 * - `../views/dir/Page.vue`  -> path: `/dir/page`, name: `dir-page`
 * - `../views/dir/index.vue` -> path: `/dir`, name: `dir`
 * - `../views/index.vue`     -> path: `/`, name: `index`
 */
function generateRoutes() {
  // 使用 vite 的 import.meta.glob 进行文件遍历，实现路由懒加载
  const files = import.meta.glob('../views/**/*.vue', { eager: false });
  const routes = [];

  for (const path in files) {
    if (Object.hasOwnProperty.call(files, path)) {
      // 从路径中提取路由名称, 例如: 'user/profile' from '../views/user/profile.vue'
      const routeName = path.match(/\.\/views\/(.*)\.vue$/)[1];

      let routePath;
      let name;

      if (routeName === 'index') {
        // 根目录的 index.vue -> /
        routePath = '/';
        name = 'index';
      } else if (routeName.endsWith('/index')) {
        // 子目录的 index.vue -> /dir
        const baseName = routeName.slice(0, -6);
        routePath = `/${baseName.toLowerCase()}`;
        name = baseName.replace(/\//g, '-');
      } else {
        // 其他 .vue 文件
        routePath = `/${routeName.toLowerCase()}`;
        name = routeName.replace(/\//g, '-');
      }

      const routeConfig = {
        path: routePath,
        name: name,
        component: files[path],
      };
      routes.push(routeConfig);
    }
  }

  // console.log(routes);
  return routes;
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: generateRoutes(),
});

// 🔧 移除路由守卫：App.vue 中的 watch 已经处理了主题切换

export default router;
