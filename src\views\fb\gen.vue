<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="openfblx()">打开粉笔页面</a-button>
          <a-button @click="openfbbg()">打开粉笔报告</a-button>
          <a-button @click="togglean()">显示答案</a-button>
          <a-button @click="pushallans()">上传答案</a-button>
          <a-button @click="toggleTop()">隐藏顶部内容</a-button>
          <a-button @click="showvideo = !showvideo">{{
            showvideo === true ? '隐藏视频' : '显示视频'
          }}</a-button>
          <a-button :type="zuoti === true ? 'primary' : 'dashed'" @click="zuotimode()"
            >做题模式{{ zuoti === true ? '开' : '关' }}</a-button
          >
          <a-button :type="zuoti === true ? 'primary' : 'dashed'" @click="submit()">提交</a-button>
          <a-button @click="toggleBackground">背景</a-button>
          <a-button @click="copyText">复制</a-button>
          <a-button @click="copyText(2)">复制2</a-button>
        </div>

        <p style="color: #00ff05" @click="toggleTop">==================</p>
        <div v-if="isLoading">Loading...</div>
        <div v-if="!isLoading">
          <div v-if="data[current1].index === 1" style="color: #00ff05">================</div>
          <div v-if="data[current1].material" v-html="data[current1].material"></div>
          <div v-html="data[current1].content"></div>
          <div class="item">
            <a-row>
              <a-col :span="24">
                <p
                  class="an_a"
                  :style="{ color: data[current1].answerColor.a }"
                  v-html="data[current1].answerone"
                ></p>
              </a-col>
              <a-col :span="24"
                ><p
                  class="an_b"
                  :style="{ color: data[current1].answerColor.b }"
                  v-html="data[current1].answertwo"
                ></p>
              </a-col>
              <a-col :span="24"
                ><p
                  class="an_c"
                  :style="{ color: data[current1].answerColor.c }"
                  v-html="data[current1].answerthree"
                ></p>
              </a-col>
              <a-col :span="24"
                ><p
                  class="an_d"
                  :style="{ color: data[current1].answerColor.d }"
                  v-html="data[current1].answerfour"
                ></p>
              </a-col>
            </a-row>
          </div>
          <p style="color: #00ff05" @click="toggleTop">==================</p>
          <div>
            <span style="color: blue" @click="open_comment(data[current1].id)">
              {{ data[current1].source }}{{ data[current1].createdTime
              }}{{ '正确率：' + Math.round(data[current1].correctRatio) }}{{ '易错项：'
              }}{{
                +data[current1].mostWrongAnswer === 0
                  ? 'A'
                  : +data[current1].mostWrongAnswer === 1
                    ? 'B'
                    : +data[current1].mostWrongAnswer === 2
                      ? 'C'
                      : 'D'
              }}</span
            >
            <Shuxue :content="data[current1].ds" />
          </div>
          <div v-show="showContent" class="answer">
            <div @click="open_comment(data[current1].id)">
              <span style="color: blue">{{ data[current1].choice }}</span>
            </div>

            <br />
            <div>
              <div v-html="data[current1].solution"></div>
            </div>
            <p>
              <a-button @click="toggleVideo(data[current1].id)">点击显示视频</a-button>
            </p>
            <p v-if="visibleVideos.includes(data[current1].id)">
              <dp1 :url="data[current1].id" :uid="data[current1].id"></dp1>
            </p>
            <comment :uid="data[current1].id" :type="value1"></comment>
            <p>==================</p>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
  <div class="fixed-buttons">
    <!-- 左下角按钮组 -->
    <div class="btn-group left">
      <button class="btn" @click="left">Left</button>
      <button class="btn" @click="right">Right</button>
    </div>
    <!-- 右下角按钮组 -->
    <div class="btn-group right">
      <button class="btn" @click="left">Left</button>
      <button class="btn" @click="right">Right</button>
    </div>
    <button type="button" class="top_button" @click="scrollToTop">top</button>
  </div>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import { message } from 'ant-design-vue';
  import dp from '../../components/dp.vue';
  import Shuxue from '@/components/shuxue.vue';

  const data = ref([]);
  const isLoading = ref(true);
  const current1 = ref(0);
  const currentindex = ref(0);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const showTop = ref(true);
  const total = ref(210);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const kaojuanid = ref('');
  const ids = ref('');
  const fbtimuid = ref(1);
  const daanid = ref('');
  const zuoti = ref(false);
  const showvideo = ref(false);
  const isBackgroundActive = ref(false);
  const mode = ref('xingce');
  const options = ref(['xingce', 'jiaoshi']);
  const visibleVideos = ref([]);
  const currentinfo = ref([]);
  const toggleBackground = async () => {
    if (isBackgroundActive.value) {
      document.body.style.background = '#fcf2d7 url(/bg_paper_mid.jpg)';
    } else {
      document.body.style.background = '';
    }
    isBackgroundActive.value = !isBackgroundActive.value;
  };
  const zuotimode = () => {
    zuoti.value = !zuoti.value;
  };
  const scrollToTop = async () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  const current_data = () => {
    console.log('1', current1.value, data.value[current1.value]);
    currentinfo.value = [];
    currentinfo.value.push(data.value[current1.value - 1]);
  };
  const toggleVideo = (id) => {
    if (visibleVideos.value.includes(id)) {
      visibleVideos.value = visibleVideos.value.filter((videoId) => videoId !== id);
    } else {
      visibleVideos.value.push(id);
    }
  };
  const value1 = ref(656604);

  const open_comment = (id) => {
    window.open(
      '/fb/comment?id=' + id + `&type=` + value1.value,
      '_blank',
      'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=800,height=600',
    );
  };
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const openfblx = () => {
    const type = route.query.type;
    let mode, mode1;
    if (type === 'js') {
      mode = 'jszgz';
      mode1 = 'jszgjy';
    } else {
      mode = 'xingce';
      mode1 = 'xingce';
    }
    window.open(
      `https://www.fenbi.com/spa/tiku/exam/practice/${mode}/${mode1}/` +
        fbtimuid.value +
        `/2?exerciseTimeMode=2`,
    );
  };
  const openfbbg = () => {
    const type = route.query.type;
    let mode, mode1;
    if (type === 'js') {
      mode = 'jszgz';
      mode1 = 'jszgjy';
    } else if (type === 'syzc') {
      mode = 'syzc';
      mode1 = 'syzc';
    } else {
      mode = 'xingce';
      mode1 = 'xingce';
    }
    //spa.fenbi.com/ti/exam/solution/1_3e_251p6rq
    https: window.open(`https://spa.fenbi.com/ti/exam/solution/${fbtimuid.value}?routecs=${mode1}`);
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = async (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      if (current1.value > 1) {
        current1.value -= 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      if (current1.value < 10000) {
        current1.value += 1;
        await getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          await ansblack();
          showContent.value = false;
        }
      }
    }
  };

  const right = () => {
    // console.log(`data.len`, data.value.length);
    if (current1.value < data.value.length - 1) {
      current1.value += 1;
      console.log(current1.value);
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 0) {
      current1.value -= 1;
      getData();
      ansblack();

      if (showContent.value) {
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = async (event) => {
    if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      if (showContent.value) {
        // isDanger(false);
        showContent.value = false;
        await ansblack();
      } else {
        // ansred();
        // const showAnsButton = document.querySelector('.showans');
        // showAnsButton.click();
        showContent.value = true;
        await ansred();
      }
    }
  };

  const ansred = async () => {
    // await nextTick();
    //
    // await ansblack();
    // console.log(document.getElementsByClassName('an_a'));
    // for (let i = 0; i < data.value.length; i++) {
    //   // console.log(data.value);
    //   if (data.value && data.value[i].choice === 'A') {
    //     document.getElementsByClassName('an_a')[i].style.color = 'blue';
    //   }
    //   if (data.value && data.value[i].choice === 'B') {
    //     document.getElementsByClassName('an_b')[i].style.color = 'blue';
    //   }
    //   if (data.value && data.value[i].choice === 'C') {
    //     document.getElementsByClassName('an_c')[i].style.color = 'blue';
    //   }
    //   if (data.value && data.value[i].choice === 'D') {
    //     document.getElementsByClassName('an_d')[i].style.color = 'blue';
    //   }
    //   if (data.value && data.value[i].answer === 'A') {
    //     document.getElementsByClassName('an_a')[i].style.color = 'red';
    //   }
    //   if (data.value && data.value[i].answer === 'B') {
    //     document.getElementsByClassName('an_b')[i].style.color = 'red';
    //   }
    //   if (data.value && data.value[i].answer === 'C') {
    //     document.getElementsByClassName('an_c')[i].style.color = 'red';
    //   }
    //   if (data.value && data.value[i].answer === 'D') {
    //     document.getElementsByClassName('an_d')[i].style.color = 'red';
    //   }
    // }
  };

  const ansblack = async () => {
    // await nextTick();
    //
    // for (let i = 0; i < data.value.length; i++) {
    //   document.getElementsByClassName('an_a')[i].style.color = 'black';
    //   document.getElementsByClassName('an_b')[i].style.color = 'black';
    //   document.getElementsByClassName('an_c')[i].style.color = 'black';
    //   document.getElementsByClassName('an_d')[i].style.color = 'black';
    // }
  };

  const anscolor = async (index, answer) => {
    console.log(index, answer);

    // Clear previous colors for the current question
    const answers = ['A', 'B', 'C', 'D'];
    answers.forEach((ans) => {
      const className = `an_${ans.toLowerCase()}`;
      const element = document.getElementsByClassName(className)[index];
      if (element) {
        element.style.color = ''; // Reset color
      }
    });

    // Set the color of the selected answer to green
    const className = `an_${answer.toLowerCase()}`;
    const element = document.getElementsByClassName(className)[index];
    if (element) {
      element.style.color = '#2bc8a0';
    }
  };

  const togglean = async (answer) => {
    if (showContent.value) {
      isDanger(false);
      showContent.value = false;
    } else {
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (id, answer, index) => {
    const url = '/egg/fbchoice';
    const type = route.query.type || 'sy';
    // console.log(type);
    const response = await axios.post(url, {
      id: id,
      choice: answer,
      type: type,
    });
    if (response.data.affectedRows !== 0) {
      message.success({
        content: `选择${answer}成功`,
        style: {
          marginTop: '80vh',
        },
      });
      await getData();
      await anscolor(index, answer);

      // await ansred();
      // await ansblack();
      // showContent.value = true;
    }
    if (zuoti.value === true) {
      const urlx = '/egg/fbincr';
      if (type === 'js') {
        mode.value = 'jszgjy';
      } else {
        mode.value = 'xingce';
      }
      const response = await axios.post(urlx, {
        id: id,
        choice: answer,
        mode: mode.value,
        index: index,
        kjid: route.query.kjid,
      });
      if (response.data.msg) {
        message.success({
          content: `做题模式选择${answer}成功`,
          style: {
            marginTop: '60vh',
          },
        });
        await getData();
        // await ansred();
        // await ansblack();
        // showContent.value = true;
      }
    }
  };
  const submit = async () => {
    const urlx = '/egg/fbsubmit';
    const type = route.query.type;
    if (type === 'js') {
      mode.value = 'jszgjy';
    } else {
      mode.value = 'xingce';
    }
    const response = await axios.post(urlx, {
      kjid: route.query.kjid,
      mode: mode.value,
    });
    if (response.data.msg) {
      message.success({
        content: `提交成功`,
        style: {
          marginTop: '80vh',
        },
      });
      // await getData();
      // await ansred();
      // await ansblack();
      // showContent.value = true;
    }
  };
  const pushallans = async () => {
    let list = data.value;
    let daan = daanid.value;
    let daanlist = daan.split('');
    daanlist = daanlist.map((item) => {
      if (+item === 1) {
        return 'A';
      }
      if (+item === 2) {
        return 'B';
      }
      if (+item === 3) {
        return 'C';
      }
      if (+item === 4) {
        return 'D';
      }
      return item; // This is necessary to return the unchanged item for other values
    });
    console.log(daanlist);
    for (let i = 0; i < list.length; i++) {}
    list.map((item, index) => {
      console.log(item.id, daanlist[index]);
      pushans(item.id, daanlist[index]);
    });
  };

  const isDanger = (answer) => {
    if (data.value && data.value.referenceAnswer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };
  const copyText = async (type = 1) => {
    let datax = data.value[current1.value];
    console.log(datax);

    // 将<p>和</p>替换为换行符\n，并删除所有其他的HTML标签
    let contentWithoutHtml = datax.content.replace(`fenbike.cn`, `fbstatic.cn`);
    // .replace(/<p>/gi, '') // 删除开头的<p>标签
    // .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
    // .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solutionWithoutHtml = datax.solution.replace(`fenbike.cn`, `fbstatic.cn`);
    // .replace(/<p>/gi, '') // 删除开头的<p>标签
    // .replace(/<\/p>/gi, '\n') // 将结束的</p>标签替换为换行符
    // .replace(/<[^>]*>/g, ''); // 删除所有其他HTML标签
    let solution = '';
    solution = +type === 2 ? solutionWithoutHtml : '';
    // 构建最终的文本
    let text = `
  ${contentWithoutHtml}\n
  ${datax.answerone}\n
  ${datax.answertwo}\n
  ${datax.answerthree}\n
  ${datax.answerfour}\n
  ====================================
  \n这题从什么信息入手可以在考场上快速秒杀\n
  \n${solution}\n
  \n这题从什么信息入手可以在考场上快速秒杀\n`;

    // 将文本复制到剪贴板
    await navigator.clipboard.writeText(text);
  };
  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'gwy';
    const gen = route.query.gen || 1;
    const kjid = route.query.kjid || 1;
    const z = route.query.type === 'zlfx' ? 1 : route.query.z || 0;
    // const f = new URLSearchParams(window.location.search).get('f');
    window.scrollTo({ top: 0, behavior: 'smooth' });

    const url = '/egg/fbgethtimu';
    let ids = [];
    let params = {
      per: per,
      page: page,
      id: id,
      z: z,
      type: type,
      isfive: 1,
      biao: kaojuanid.value,
      gen: gen,
      kjid: kjid,
      ids: ids.value,
    };
    console.log(params);
    // await ansblack();
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   await getData();
      //   return;
      // }
      if (a) {
        showContent.value = true;
      }
      fbtimuid.value = kjid;
      const solutions = response.data;
      data.value = solutions.map((item, index) => {
        ids.push({ sort: index, id: item.id });
        // const options = item.accessories[0].options;
        const choiceMap = ['A', 'B', 'C', 'D'];
        // const correctIndex = parseInt(item.anwser, 10);

        return {
          ...item,

          id: item.id,
          content: item.content.replace(
            /<p>/,
            '<p><span style="color:red;">' +
              `(` +
              (+item.choice === 0
                ? 'A'
                : +item.choice === 1
                  ? 'B'
                  : +item.choice === 2
                    ? 'C'
                    : 'D') +
              `)` +
              '</span>.' +
              (index + 1) +
              '.',
          ),
          answerone: `A.` + item.answerone,
          answertwo: `B.` + item.answertwo,
          answerthree: `C.` + item.answerthree,
          answerfour: `D.` + item.answerfour,
          // choice: choiceMap[correctIndex], // 正确选项字母
          solution: item.solution,
          source: item.source || '',
          createdTime: '', // 可填补更多字段
          answerColor: {
            a: item.answer === 'A' ? 'red' : 'black',
            b: item.answer === 'B' ? 'red' : 'black',
            c: item.answer === 'C' ? 'red' : 'black',
            d: item.answer === 'D' ? 'red' : 'black',
          },
        };
      });
      const getds = await axios.post(`/egg/getds?biao=fbsy`, { data: ids });

      // for (let item in solutions) {
      //   solutions[item].ds = getds.data[item].ds;
      // }
      data.value.forEach((it, i) => (it.ds = getds.data[i].ds));
      console.log(`data.value`, data.value);
      await nextTick();

      console.log(data.value);
      let currentparentid = 0;
      let index = 0;
      for (let item of data.value) {
        if (item?.parentid !== currentparentid) {
          item.index = 1;
          currentparentid = item.parentid;
        } else {
          item.index = 0;
        }
        if (!item.solution.match(/<p>A/g)) {
          item.solution = item.solution.replace(/A项/g, '<br/><br/>A项');
        }
        if (!item.solution.match(/<p>B/g)) {
          item.solution = item.solution.replace(/B项/g, '<br/><br/>B项');
        }
        if (!item.solution.match(/<p>C/g)) {
          item.solution = item.solution.replace(/C项/g, '<br/><br/>C项');
        }
        if (!item.solution.match(/<p>D/g)) {
          item.solution = item.solution.replace(/D项/g, '<br/><br/>D项');
        }
        // console.log(item);
      }
      console.log(data.value);
      await copyText(2);
      showContent.value = true;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
    document.body.style.background = 'RGB(250, 249, 222)';
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .fixed-buttons {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
  }
  .top_button {
    opacity: 0.3;
    background-color: #a8a8a8;
    pointer-events: all;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 999;
    padding: 10px;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  }
  .btn-group {
    display: flex;

    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 60px;
    height: 40px;
    opacity: 0.3;
    background-color: #a8a8a8;
    color: white;
    border: none;
    outline: none;
    cursor: pointer;
    pointer-events: all;
  }

  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }
  div {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
    font-size: 20px;
  }
  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
