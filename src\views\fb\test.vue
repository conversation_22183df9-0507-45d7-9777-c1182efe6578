<!-- TestScroll.vue -->
<script setup></script>

<template>
  <!-- 整行撑满视口高度 -->
  <a-row class="full-height">
    <!-- 左侧栏，固定宽度，高度 100% -->
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" class="side-col">
      <div class="lb" @click="">左侧</div>
    </a-col>

    <!-- 中间可滚动区域 -->
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center-col">
      <div class="center-container">
        <!-- 这层做 flex:1 + overflow -->
        <div class="scroll-area">
          <div class="scroll-content">
            <!-- 模拟大量内容 -->
            <div v-for="i in 100" :key="i">内容 {{ i }}</div>
          </div>
        </div>
      </div>
    </a-col>

    <!-- 右侧栏，固定宽度，高度 100% -->
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" class="side-col">
      <div class="rb" @click="">右侧</div>
    </a-col>
  </a-row>
</template>

<style scoped>
  /* 让行和列都撑满视口，禁止全局滚动 */
  .full-height {
    height: 100vh;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  /* ant-design 的 a-row / a-col，有时会有默认 display，确保高度继承 */
  a-row,
  a-col {
    height: 100%;
  }

  /* 左右侧固定列，内容撑满 */
  .side-col .lb,
  .side-col .rb {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 中间列，建立 flex 容器 */
  .center-col {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* 外层容器，可根据需要再加 header/footer */
  .center-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* 真正滚动的区域 */
  .scroll-area {
    flex: 1;
    overflow-y: auto;
  }

  /* 内容内部样式 */
  .scroll-content {
    padding: 16px;
  }

  /* 调试用边框 */
  * {
    border: 1px solid #333;
    box-sizing: border-box;
  }
</style>
