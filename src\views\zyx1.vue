<script>
  export default {
    name: 'Zyx1',
    data: function () {
      return {
        data: [],
        columns: [
          {
            title: '序号',
            dataIndex: 'position_code',
          },
          {
            title: '公司',
            dataIndex: 'unit_name',
          },
          {
            title: '职位',
            dataIndex: 'job_title',
          },
          {
            title: '通过人数',
            dataIndex: 'pass_count',
            sorter: {
              compare: (a, b) => a.pass_count - b.pass_count,
              multiple: 3,
            },
          },
          {
            title: '未通过人数',
            dataIndex: 'no_pass_count',
            sorter: {
              compare: (a, b) => a.no_pass_count - b.no_pass_count,
              multiple: 2,
            },
          },
          {
            title: '未审核人数',
            dataIndex: 'no_check_count',
            sorter: {
              compare: (a, b) => a.no_check_count - b.no_check_count,
              multiple: 1,
            },
          },
          {
            title: '报名总数',
            dataIndex: 'total_count',
            sorter: {
              compare: (a, b) => a.total_count - b.total_count,
              multiple: 0,
            },
          },
        ],
      };
    },
    mounted() {
      this.getData();
      function pagination() {
        total: 200;
      }
    },
    methods: {
      getData() {
        axios.get('/egg/zyx').then((res) => {
          this.data = res.data.data.list;
        });
      },
      indexMethod(index) {
        return index + 1;
      },
      getRowClassName(record) {
        // 根据条件返回不同的行样式
        if (record.job_id === 1565) {
          return 'backgroundColor';
        } else {
          return null; // 不应用任何额外的样式
        }
      },
    },
  };
</script>

<template>
  <a-table
    class="ant-table-striped"
    :columns="columns"
    :data-source="data"
    :pagination="false"
    :row-class-name="getRowClassName"
  />
</template>

<style scoped>
  ant-table-tbody .backgroundColor {
    background-color: #7ec1ff !important;
  }
</style>
<style src="../../public/zyx.css"></style>
