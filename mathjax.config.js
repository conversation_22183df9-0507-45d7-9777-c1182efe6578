// mathjax-utils.js
import { mathjax } from 'mathjax-full/js/mathjax';
import { TeX } from 'mathjax-full/js/input/tex';
import { SVG } from 'mathjax-full/js/output/svg';
import { liteAdaptor } from 'mathjax-full/js/adaptors/liteAdaptor';
import { RegisterHTMLHandler } from 'mathjax-full/js/handlers/html';
import { AllPackages } from 'mathjax-full/js/input/tex/AllPackages';

// 创建适应器
const adaptor = liteAdaptor();
RegisterHTMLHandler(adaptor);

// 配置 MathJax
const tex = new TeX({
  packages: AllPackages,
  inlineMath: [
    ['$', '$'],
    ['\\(', '\\)'],
  ],
  displayMath: [
    ['$$', '$$'],
    ['\\[', '\\]'],
  ],
});

const svg = new SVG({ fontCache: 'local' });
const html = mathjax.document('', { InputJax: tex, OutputJax: svg });

// 初始化 MathJax
export function initMathJax() {
  return new Promise((resolve) => {
    // 确保 MathJax 在浏览器环境中运行
    if (typeof window !== 'undefined') {
      window.MathJax = {
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)'],
          ],
          packages: AllPackages,
        },
        svg: {
          fontCache: 'local',
        },
        startup: {
          ready: () => {
            // 保持默认启动
            mathjax.startup.defaultReady();
            resolve();
          },
        },
      };
    } else {
      resolve(); // 在非浏览器环境中直接 resolve
    }
  });
}

// 渲染数学公式
export function typeset() {
  if (typeof document === 'undefined') return; // 避免在服务器端运行

  const mathElements = document.querySelectorAll('.math-rendered');
  mathElements.forEach((element) => {
    html.reset();
    html.findMathInElement(element);
    html.Typeset();
  });
}
