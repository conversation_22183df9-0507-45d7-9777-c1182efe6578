<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="lb" style="" @click="left"></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <div class="wp">
        <div v-show="showTop">
          <span class="top-input">
            <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
            <a-button @click="prev_page">p</a-button>
            <a-input-number
              id="inputNumber"
              v-model:value="current_page"
              :min="1"
              @change="getData"
            />
            <a-button @click="next_page">n</a-button>
            <a-button @click="allshow">all</a-button>
            <div
              v-show="showCanvas"
              style="
                background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
                position: fixed;
                top: 0;
                position: relative;
              "
            >
              <div class="top-button">
                <a-button @click="toggleCanvas">画布1</a-button>
                <a-button @click="initCanvas">init画布</a-button>
              </div>
            </div>
          </span>
        </div>

        <canvas
          v-show="showCanvas"
          id="drawingCanvas"
          style="position: absolute; background-color: rgba(255, 255, 255, 0)"
          @mousedown="startDrawing"
          @mouseup="stopDrawing"
          @mousemove="draw"
          @mouseleave="stopDrawing"
          @touchstart="startDrawingTouch"
          @touchmove="drawTouch"
          @touchend="stopDrawing"
        ></canvas>
        <div v-html="zltimu.content"></div>

        <a-row>
          <a-col
            v-for="(item, index) in data"
            :key="index"
            :span="data.length === 2 ? 12 : 24"
            class="datalist"
          >
            <div v-if="isLoading">Loading...</div>
            <div v-if="!isLoading" :class="index === 1 ? 'items' : ''">
              <!--              <div>{{ resolution }}</div>-->
              <div class="maintimu" v-html="item.content"></div>
              <!-- <fbtagVVV
                  :messageFromParent="item.id"
                  :messageFromParent1="typex"
                ></fbtag> -->
              <div class="item" @click="togglean">
                <p class="an_a" @click="pushans(item.id, 'A')" v-html="item.answerone"></p>
                <p class="an_b" @click="pushans(item.id, 'B')" v-html="item.answertwo"></p>
                <p class="an_c" @click="pushans(item.id, 'C')" v-html="item.answerthree"></p>
                <p class="an_d" @click="pushans(item.id, 'D')" v-html="item.answerfour"></p>
              </div>
              <p @click="toggleTop">================</p>
              <div v-show="showContent" class="answer">
                <div>
                  <span style="color: blue">{{ item.choice }}</span>
                  {{ item.source }}{{ item.createdTime }}
                </div>
                <br />
                <div v-if="daanblock">
                  <div :style="showdaanblock" v-html="item.solution"></div>
                </div>
                <p>==================</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-show="showTop" class="top">
        <span class="top-input">
          <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
          <a-button @click="prev_page">p</a-button>
          <a-input-number
            id="inputNumber"
            v-model:value="current_page"
            :min="1"
            @change="getData"
          />
          <a-button @click="next_page">n</a-button>
          <a-button @click="toggledaanblock">隐藏答案块</a-button>
          <a-button @click="toggleshowdaan">
            {{ showdaan === true ? '已' : '不' }}显示答案{{ total }}
          </a-button>
          <a-button @click="recoverpage">恢复页数</a-button>
        </span>
        <div class="top-button">
          <a-button class="showans" @click="togglean">显示答案</a-button>
          <a-button @click="toggleansblock">隐藏答案</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <!--          <fbhiddentag></fbhiddentag>-->
          <a-button @click="toggleCanvas">画布</a-button>
          <a-button @click="initCanvas">init画布</a-button>
          <a-button @click="savepage">保存页数</a-button>
        </div>
      </div>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="rb" style="" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';

  export default {
    name: 'Min',
    components: {
      fbtag,
      fbhiddentag,
    },
    setup() {
      const zltimu = ref([]);
      const data = ref([]);
      const isLoading = ref(true);
      const showTop = ref(true);
      const current_page = ref(1);
      const showContent = ref(false);
      const pagetotal = ref(20);
      const perpage = ref(1);
      const total = ref(0);
      const route = useRoute();
      const fontStyle = ref(16);
      const isTagHidden = ref(false);
      const typex = ref('sy');
      const showdaan = ref(false);
      const showdaanblock = ref('');
      const showdaanblock1 = ref(true);
      const resolution = ref('');
      const open_count = ref(0);
      const drawing = ref(false);
      const showCanvas = ref(false);
      const ctx = ref(null);
      const daanblock = ref(true);
      const allshow = async () => {
        current_page.value = 1;
        perpage.value = +total.value;
        await getData();
      };
      const getResolution = () => {
        resolution.value = `${window.innerWidth} x ${window.innerHeight}`;
      };
      const toggleHiddenTag = () => {
        // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
        const elements = document.querySelectorAll('.tag');

        // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
        elements.forEach((element) => {
          if (isTagHidden.value) {
            element.style.display = 'block'; // 或者其他适当的显示样式
          } else {
            element.style.display = 'none';
          }
        });

        // 切换状态变量
        isTagHidden.value = !isTagHidden.value;
      };

      const toggleTop = () => {
        showTop.value = !showTop.value;
      };
      const toggledaanblock = () => {
        daanblock.value = !daanblock.value;
      };

      const prev_page = () => {
        left();
      };
      const next_page = () => {
        right();
      };

      const toggleshowdaan = async () => {
        showdaan.value = !showdaan.value;
        await initCanvas();
      };

      const toggleCanvas = async () => {
        showCanvas.value = !showCanvas.value;
      };
      const toggleansblock = async () => {
        showContent.value = !showContent.value;
        await ansblack();
      };
      const handleKeyDown = async (event) => {
        if (event.key === 'ArrowLeft' || event.key === 'q') {
          await left();
        } else if (event.key === 'ArrowRight' || event.key === 'e') {
          await right();
        } else if (event.key === 'h') {
          const showAnsButton = document.querySelector('.an_a');
          await showAnsButton.click();
        } else if (event.key === 'j') {
          const showAnsButton = document.querySelector('.an_b');
          await showAnsButton.click();
        } else if (event.key === 'k') {
          const showAnsButton = document.querySelector('.an_c');
          await showAnsButton.click();
        } else if (event.key === 'l') {
          const showAnsButton = document.querySelector('.an_d');
          await showAnsButton.click();
        } else if (event.key === 'u') {
          await toggleCanvas();
        } else if (event.key === 'i') {
          await initCanvas();
        } else if (event.key === 's') {
          await savepage();
        } else if (event.key === 'r') {
          await recoverpage();
        } else if (event.key === '1') {
          await pushans(data.value[0].id, 'A');
        } else if (event.key === '2') {
          await pushans(data.value[0].id, 'B');
        } else if (event.key === '3') {
          await pushans(data.value[0].id, 'C');
        } else if (event.key === '4') {
          await pushans(data.value[0].id, 'D');
        }
      };

      const updateValue = async (delta) => {
        open_count.value = 0;
        const newValue = +current_page.value + delta;
        if (newValue >= 1 && newValue <= 100000) {
          current_page.value = newValue;
          await getData();
          if (!showdaan.value) {
            showContent.value = false;
            await ansblack();
          } else {
            showContent.value = true;
            await ansred();
          }
        }
      };

      const right = async () => {
        await updateValue(1);
      };

      const left = async () => {
        await updateValue(-1);
      };

      const toggleContent = async (event) => {
        if (
          event.key === ' ' ||
          event.key === 'ArrowUp' ||
          event.key === 'ArrowDown' ||
          event.key === 'Spacer' ||
          event.key === 'w' ||
          event.key === 'x'
        ) {
          await getData();
          if (showContent.value) {
            await ansblack();
            showContent.value = false;
          } else {
            await ansred();
            const showAnsButton = document.querySelector('.showans');
            showAnsButton.click();
            showContent.value = true;
          }
        }
      };

      const ansred = async () => {
        await ansblack();
        for (let i = 0; i < data.value.length; i++) {
          // console.log(data.value);
          if (data.value && data.value[i].choice === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].answer === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'red';
          }
        }
      };

      const ansblack = async () => {
        for (let i = 0; i < data.value.length; i++) {
          document.getElementsByClassName('an_a')[i].style.color = 'black';
          document.getElementsByClassName('an_b')[i].style.color = 'black';
          document.getElementsByClassName('an_c')[i].style.color = 'black';
          document.getElementsByClassName('an_d')[i].style.color = 'black';
        }
      };

      const togglean = async (answer) => {
        if (showContent.value && !showdaan.value && open_count.value === 0) {
          await ansblack();
          showContent.value = false;
        } else {
          open_count.value++;
          await ansred();
          showContent.value = true;
        }
        // console.log('打开次数', open_count.value);
      };
      const recoverpage = async () => {
        const url = '/egg/fbrecoverpage';
        const id = route.query.id;
        const b = route.query.b || 0;
        if (b === 0) {
          let params = {
            id: id,
          };
          const response = await axios.get(url, { params });
          current_page.value = response.data.page || current_page.value;
          console.log(current_page.value);
        }
        await getData();
        await ansblack();
      };
      const savepage = async () => {
        const url = '/egg/fbremeber';
        const type = route.query.type || 'sy';
        const id = route.query.id || 'sy';
        const b = route.query.b || 0;
        // console.log(type);
        if (b === 0) {
          const response = await axios.post(url, {
            id: id,
            page: current_page.value,
          });
          if (+response.data.affectedRows !== 0) {
            console.log(`记住${current_page.value}成功`);
            message.success({
              content: `记住${current_page.value}成功`,
              duration: 6,
              style: {
                marginTop: '80vh',
              },
            });
          }
        }
      };
      const pushans = async (id, answer) => {
        const url = '/egg/fbchoice';
        const type = route.query.type || 'sy';
        console.log(type);
        const response = await axios.post(url, {
          id: id,
          choice: answer,
          type: type,
        });
        // console.log(response);
        if (response.data.affectedRows !== 0) {
          message.success({
            content: `选择${answer}成功`,
            style: {
              marginTop: '80vh',
            },
          });
          await getData();
          await ansred();
          await savepage();
          showContent.value = true;
        }
      };
      const initCanvas = async () => {
        const canvas = document.getElementById('drawingCanvas');
        canvas.width = document.querySelector('.wp').offsetWidth;
        canvas.height = document.querySelector('body').offsetHeight;
        ctx.value = canvas.getContext('2d');
      };

      const startDrawing = (event) => {
        drawing.value = true;
        draw(event);
      };

      const stopDrawing = () => {
        drawing.value = false;
        ctx.value.beginPath();
      };

      const draw = (event) => {
        if (!drawing.value) return;
        ctx.value.strokeStyle = 'blue';
        ctx.value.lineWidth = 3;
        ctx.value.lineCap = 'round';
        ctx.value.lineTo(event.offsetX, event.offsetY);
        ctx.value.stroke();
        ctx.value.beginPath();
        ctx.value.moveTo(event.offsetX, event.offsetY);
      };

      const startDrawingTouch = (event) => {
        event.preventDefault();
        const touch = event.touches[0];
        const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
        const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
        startDrawing({
          offsetX,
          offsetY,
        });
      };

      const drawTouch = (event) => {
        event.preventDefault();
        const touch = event.touches[0];
        const offsetX = touch.clientX - event.target.getBoundingClientRect().left;
        const offsetY = touch.clientY - event.target.getBoundingClientRect().top;
        draw({
          offsetX,
          offsetY,
        });
      };

      const getData = async () => {
        perpage.value = +perpage.value;
        const per = route.query.per || perpage.value;
        const a = route.query.a || false;
        const id = route.query.id || 1;
        const z = route.query.z || 0;
        const b = route.query.b || 0;
        const t = route.query.t || 0;
        const page = route.query.page || current_page.value;
        const type = route.query.type || 'rk';
        // const f = new URLSearchParams(window.location.search).get('f');
        const url = '/egg/fbtimu';
        let params = {
          per: per,
          page: page,
          id: id,
          type: type,
          z: z,
          b: b,
          t: !t ? 0 : t,
        };
        // console.log(params);
        try {
          const response = await axios.get(url, { params });
          // if (response.data.length === 0) {
          //   console.log('z1', z);
          //   await getData();
          //   return;
          // }
          if (a) {
            showContent.value = true;
          }
          // console.log(response.data.pagetotal[0].total);
          data.value = response.data.data;
          let index = 1;
          for (let item in data.value) {
            data.value[item].content =
              perpage.value === 1
                ? `<p><span style="color:${
                    data.value[item].choice !== null ? '#00BFFF' : '#000'
                  }">${current_page.value}</span>.` + data.value[item].content
                : `<p><span style="color:${
                    data.value[item].choice !== null ? '#00BFFF' : '#000'
                  }">${index}</span>.` + data.value[item].content;
            index++;
            if (!data.value[item].solution.match(/<p>A/g)) {
              data.value[item].solution = data.value[item].solution.replace(
                /A项/g,
                '<br/><br/>A项',
              );
            }
            if (!data.value[item].solution.match(/<p>B/g)) {
              data.value[item].solution = data.value[item].solution.replace(
                /B项/g,
                '<br/><br/>B项',
              );
            }
            if (!data.value[item].solution.match(/<p>C/g)) {
              data.value[item].solution = data.value[item].solution.replace(
                /C项/g,
                '<br/><br/>C项',
              );
            }
            if (!data.value[item].solution.match(/<p>D/g)) {
              data.value[item].solution = data.value[item].solution.replace(
                /D项/g,
                '<br/><br/>D项',
              );
            }
          }
          if (+z === 1) {
            zltimu.value = response.data.data[0];
            // console.log(zltimu.value);
            data.value = response.data.data[1];
          }
          if (+b === 1) {
            for (let item in data.value) {
              // console.log(data.value[item].answer);
              data.value[item].solution = '';
            }
          }
          total.value = response.data.pagetotal[0].total || 0;
          // ansblack();
          // showContent.value = false;
          isLoading.value = false;
          await initCanvas();
        } catch (error) {
          console.error(error);
          isLoading.value = false;
        }
      };

      onMounted(async () => {
        document.addEventListener('keydown', toggleContent);
        await recoverpage();
        window.addEventListener('keydown', handleKeyDown);
        getResolution();
        await initCanvas();
        window.addEventListener('resize', getResolution);
        window.addEventListener('resize', initCanvas);
      });

      onBeforeUnmount(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keydown', toggleContent);
        window.removeEventListener('resize', getResolution);
        window.removeEventListener('resize', initCanvas);
      });

      return {
        data,
        zltimu,
        isLoading,
        current_page,
        showContent,
        pagetotal,
        perpage,
        total,
        left,
        right,
        togglean,
        getData,
        pushans,
        fontStyle,
        next_page,
        toggleTop,
        showTop,
        toggleHiddenTag,
        isTagHidden,
        typex,
        toggleshowdaan,
        showdaan,
        getResolution,
        resolution,
        open_count,
        toggleansblock,
        showdaanblock,
        showdaanblock1,
        initCanvas,
        startDrawing,
        stopDrawing,
        draw,
        toggleCanvas,
        showCanvas,
        startDrawingTouch,
        drawTouch,
        prev_page,
        daanblock,
        toggledaanblock,
        savepage,
        recoverpage,
        allshow,
      };
    },
  };
</script>

<style>
  body {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
  }
  * {
    margin: 0;
    padding: 0;
    font-size: 20px;
  }
  button {
    line-height: 0 !important;
  }
  .items {
    border-left: 2px solid black;
    padding-left: 10px;
  }
  .top {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    position: fixed;
    bottom: 0;
  }
  img {
    max-width: 100%;
    max-height: 100%;
  }
  .top {
    display: flex;
    flex-direction: column;
  }

  .top-button,
  .top-input {
    display: flex;
    flex-wrap: wrap;

    gap: 10px; /* Adjust the gap as needed */
  }
  .wp {
    color: black;
    height: 100%;
    width: 100%; /* 宽度占满父容器 */
    max-width: 100vw; /* 最大宽度不超过视口宽度 */
    word-wrap: break-word; /* 在单词内部进行换行 */
    overflow-x: hidden;
    padding-top: 10px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }
  @media only screen and (max-width: 1024px) {
    * {
      margin: 0;
      padding: 0;
      font-size: 24px;
    }
  }
  @media only screen and (max-width: 576px) {
    * {
      font-size: 20px;
    }
    :where(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    :where(.css-dev-only-do-not-override-hkh161).ant-pagination {
      width: 100%;
    }
    .rb {
      background-color: rgba(240, 240, 240, 0.3);
    }
    :global(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    .top-button {
      display: block;
    }
    .top-input {
      display: block;
    }
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }

  canvas {
    border: 1px solid black;
  }
  #drawingCanvas {
    z-index: 10; /* 确保画布在内容之上 */
    max-width: 100vw; /* 确保画布宽度不超过视口宽度 */
  }
</style>
