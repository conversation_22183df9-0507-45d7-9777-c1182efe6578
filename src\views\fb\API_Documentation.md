# sy.vue API 文档

本文档详细记录了 `sy.vue` 文件中所有的 API 请求，包括组件、评论和数学相关的接口。

## 📋 目录

- [API 请求列表](#api-请求列表)
- [组件相关 API](#组件相关-api)
- [评论相关 API](#评论相关-api)
- [数学相关 API](#数学相关-api)
- [功能模块](#功能模块)
- [使用说明](#使用说明)

## 🔗 API 请求列表

### 1. 题目相关 API

#### 1.1 获取题目数据

```javascript
// 主要题目获取接口
GET / egg / fbtimu;
```

**参数说明：**

- `per`: 每页题目数量
- `page`: 当前页码
- `id`: 题目ID
- `type`: 题目类型 (默认: 'syzc')
- `z`: 参数z
- `b`: 参数b
- `f`: 参数f
- `o`: 参数o
- `q`: 查询参数
- `ids`: 题目ID列表
- `biao`: 表名 (默认: 'fbsy')
- `t`: 参数t
- `zql`: 正确率
- `kjid`: 考试ID
- `parentid`: 父级ID
- `fast`: 快速模式 ('1')

#### 1.2 获取考试题目

```javascript
// 考试模式下的题目获取
GET / egg / fbgethtimu;
```

**参数说明：**

- `fast`: 快速模式 ('1')
- 其他参数与 fbtimu 相同

#### 1.3 获取题目排名

```javascript
// 异步获取题目排名
GET / egg / gettimurank;
```

**参数说明：**

- `biao`: 表名 ('fbsy')
- `id`: 题目ID
- `allcateid`: 分类ID

### 2. 答案提交相关 API

#### 2.1 提交答案

```javascript
// 用户主动提交答案
POST / egg / fbsubmit;
```

**请求体：**

```javascript
{
  kjid: string,  // 考试ID
  mode: string   // 模式类型
}
```

#### 2.2 答案记录

```javascript
// 通过 Exercise Store 提交答案
// 内部调用多个接口进行答案记录
```

### 3. 页面状态管理 API

#### 3.1 保存页面状态

```javascript
// 保存当前页面状态
POST / egg / fbremeber;
```

**请求体：**

```javascript
{
  cateid: string | null,  // 分类ID
  kjid: string,           // 考试ID
  type: string,           // 类型
  page: number            // 页码
}
```

#### 3.2 恢复页面状态

```javascript
// 恢复页面状态
GET / egg / fbrecoverpage;
```

**参数说明：**

- `cateid`: 分类ID
- `kjid`: 考试ID
- `type`: 类型

### 4. 数据管理 API

#### 4.1 设置空值

```javascript
// 设置题目为空值
GET / egg / setnull;
```

**参数说明：**

- `id`: 题目ID
- `type`: 类型
- `biao`: 表名

#### 4.2 批量更新ID

```javascript
// 批量更新ID为空值
GET / egg / updateidsnull;
```

**参数说明：**

- `ids`: ID列表 (逗号分隔)

## 🧩 组件相关 API

### 1. Canvas 组件

```javascript
// Canvas Store 相关方法调用
canvasStore.toggleCanvasVisibility(); // 切换画布可见性
canvasStore.toggleDrawMode(); // 切换绘制模式
canvasStore.initCanvas(); // 初始化画布
canvasStore.clearCanvas(); // 清除画布
canvasStore.saveCanvasData(); // 保存画布数据
canvasStore.loadCanvasData(); // 加载画布数据
canvasStore.updateEraseSize(); // 更新擦除大小
canvasStore.toggleEraseIndicator(); // 切换擦除指示器
canvasStore.enableRealtimeTransmission(); // 启用实时传输
canvasStore.disableRealtimeTransmission(); // 禁用实时传输
canvasStore.setCurrentQuestionId(); // 设置当前题目ID
canvasStore.cleanupCanvas(); // 清理画布资源
```

### 2. Exercise Store

```javascript
// 练习相关方法
exerciseStore.toggleZuotiMode(); // 切换做题模式
exerciseStore.submitAnswer(); // 提交答案
exerciseStore.isZuotiMode; // 做题模式状态
```

### 3. Loading Store

```javascript
// 加载状态管理
loadingStore.startLoading(); // 开始加载
loadingStore.stopLoading(); // 停止加载
loadingStore.isLoading; // 加载状态
```

### 4. Theme Store

```javascript
// 主题管理
themeStore.toggleBackground(); // 切换背景
themeStore.cycleSyTheme(); // 循环切换主题
themeStore.theme; // 当前主题
themeStore.syTheme; // sy主题
```

## 💬 评论相关 API

### Comment 组件

```javascript
// 评论组件使用
<comment
  :uid="+item.id"
  :type="current_type === 'gwy' ? 48644 : 6"
/>
```

**参数说明：**

- `uid`: 用户ID (题目ID)
- `type`: 评论类型
  - 公务员: 48644
  - 其他: 6

**功能：**

- 显示题目评论
- 支持评论交互
- 根据题目类型显示不同评论

## 📐 数学相关 API

### Shuxue 组件

```javascript
// 数学公式组件
<Shuxue :content="item.ds" />
```

**参数说明：**

- `content`: 数学公式内容 (item.ds)

**功能：**

- 渲染数学公式
- 支持 LaTeX 语法
- 数学公式高亮显示

## 🎯 功能模块

### 1. 题目导航

- **上一题**: `left()` 函数
- **下一题**: `right()` 函数
- **页面跳转**: `updateValue()` 函数

### 2. 答案管理

- **答案提交**: `pushans()` 函数
- **自动记录**: `pushans1()` 函数
- **答案高亮**: `toggleAnswerHighlight()` 函数

### 3. 画布功能

- **实时绘制**: Canvas 实时传输
- **模式切换**: 绘制/擦除模式
- **数据同步**: 画布数据保存/加载

### 4. 键盘快捷键

```javascript
// 支持的快捷键
ArrowLeft/Q: 上一题
ArrowRight/E: 下一题
H: 选择A选项
J: 选择B选项
K: 选择C选项
L: 选择D选项
U: 切换画布
Ctrl+E: 切换绘制模式
Ctrl+[: 缩小擦除范围
Ctrl+]: 扩大擦除范围
Ctrl+T: 切换范围显示
```

## 📝 使用说明

### 1. 基本使用

```javascript
// 获取题目数据
await getData();

// 提交答案
await pushans(questionId, answer);

// 切换做题模式
exerciseStore.toggleZuotiMode();
```

### 2. 画布使用

```javascript
// 启用画布
canvasStore.toggleCanvasVisibility();

// 初始化画布
await canvasStore.initCanvas();

// 保存画布数据
await canvasStore.saveCanvasData();
```

### 3. 主题切换

```javascript
// 切换背景
themeStore.toggleBackground();

// 循环主题
themeStore.cycleSyTheme();
```

## 🔧 配置参数

### 路由参数

- `kjid`: 考试ID
- `type`: 题目类型
- `biao`: 表名
- `per`: 每页数量
- `page`: 页码
- `id`: 题目ID

### 状态参数

- `current_type`: 当前类型
- `current_page`: 当前页码
- `perpage`: 每页数量
- `total`: 总题目数
- `tid`: 当前题目ID

## 📊 数据流

1. **页面加载** → `recoverpage()` → 恢复状态
2. **获取数据** → `getData()` → 加载题目
3. **用户交互** → `pushans()` → 提交答案
4. **状态保存** → `savepage()` → 保存进度
5. **画布同步** → Canvas Store → 实时传输

## 🚀 性能优化

1. **异步排名加载**: 不阻塞主流程
2. **快速模式**: 跳过不必要的数据库查询
3. **防抖处理**: 防止重复触发
4. **资源清理**: 组件卸载时清理资源

## 🔍 调试信息

所有 API 请求都包含详细的调试日志：

```javascript
console.log('params', params);
console.log('data', data.value);
console.log('✅ 排名加载完成:', rankStr);
```

## 📋 注意事项

1. **网络请求**: 所有 API 都使用 axios 进行请求
2. **错误处理**: 包含完整的错误处理机制
3. **状态管理**: 使用 Pinia Store 进行状态管理
4. **响应式**: 所有数据都是响应式的
5. **类型安全**: 包含完整的类型检查

---

**文档版本**: 1.0.0  
**最后更新**: 2025-07-29  
**维护者**: AI Assistant
