// src/directives/mathjax.js

export default {
  // 当指令第一次绑定到元素时（mounted），尝试渲染该元素内的公式
  mounted(el) {
    // 注意：window._MathJax 在 main.js 里已经初始化好了
    const mj = window._MathJax;
    if (mj && mj.print && el) {
      // 传入当前元素，只渲染它及其子孙里的 LaTeX
      mj.print(el);
    }
  },
  // 当对应的父组件更新、子节点更改时（updated），再次渲染
  updated(el) {
    const mj = window._MathJax;
    if (mj && mj.print && el) {
      mj.print(el);
    }
  },
};
