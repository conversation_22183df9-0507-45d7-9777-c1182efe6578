<script>
  import { onMounted, ref, defineComponent } from 'vue';
  import axios from 'axios';

  export default defineComponent({
    name: 'Updatetimu',
    setup() {
      const data = ref([]);

      const tree = async () => {
        await axios.get('/egg/mintree').then((res) => {
          data.value = res.data;
          for (let item of data.value) {
            item.total = 0;
            item.timer = true;
          }
        });
      };

      const getData1 = async () => {
        await axios.get('/egg/min?type=1').then((res) => {
          data.value[0].total = res.data.total;
        });
      };

      const getData2 = async () => {
        await axios.get('/egg/min?type=2').then((res) => {
          data.value[2].total = res.data.total;
        });
      };

      const getData3 = async () => {
        await axios.get('/egg/min?type=3').then((res) => {
          data.value[3].total = res.data.total;
        });
      };
      const getData4 = async () => {
        await axios.get('/egg/min?type=4').then((res) => {
          data.value[4].total = res.data.total;
        });
      };
      const getData5 = async () => {
        await axios.get('/egg/min?type=5').then((res) => {
          data.value[5].total = res.data.total;
        });
      };
      const timers = [];
      const startTimer1 = () => {
        timers[1] = setInterval(async () => {
          await getData1();
        }, 3000);
      };

      const startTimer2 = () => {
        timers[2] = setInterval(async () => {
          await getData2();
        }, 3000);
      };

      const startTimer3 = () => {
        timers[3] = setInterval(async () => {
          await getData3();
        }, 3000);
      };

      const startTimer4 = () => {
        timers[4] = setInterval(async () => {
          await getData4();
        }, 3000);
      };

      const startTimer5 = () => {
        timers[5] = setInterval(async () => {
          await getData5();
        }, 3000);
      };

      const clearTimer = (timer, check) => {
        if (timer === 0 && check === false) {
          clearInterval(timers[1]);
        } else if (timer === 2 && check === false) {
          clearInterval(timers[2]);
        } else if (timer === 3 && check === false) {
          clearInterval(timers[3]);
        } else if (timer === 4 && check === false) {
          clearInterval(timers[4]);
        } else if (timer === 5 && check === false) {
          clearInterval(timers[5]);
        }
        if (timer === 0 && check === true) {
          startTimer1();
        } else if (timer === 2 && check === true) {
          startTimer2();
        } else if (timer === 3 && check === true) {
          startTimer3();
        } else if (timer === 4 && check === true) {
          startTimer4();
        } else if (timer === 5 && check === true) {
          startTimer5();
        }
      };

      onMounted(async () => {
        await tree();
        await startTimer1();
        await startTimer2();
        await startTimer3();
        await startTimer4();
        await startTimer5();
      });

      return {
        data,
        clearTimer,
      };
    },
  });
</script>

<template>
  <a-row>
    <template v-for="(item, index) in data" :key="item.objectId">
      <a-col :span="4">
        <div style="background: #ececec; padding: 30px">
          <a-card :title="item.objectName" :bordered="false" style="width: 200px">
            <p>{{ item.total }}</p>
            <p>{{ item.questionCount }}</p>
            <a-switch v-model:checked="item.timer" @change="clearTimer(index, item.timer)" />
          </a-card>
        </div>
      </a-col>
    </template>
  </a-row>
</template>

<style scoped></style>
