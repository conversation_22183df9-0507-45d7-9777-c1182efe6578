<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-button @click="increaseFontSize">增大字体</a-button>
          <a-button @click="togglean(data.referenceAnswer)">显示答案</a-button>
          <a-button>{{ pagetotal }}</a-button>
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <a-button @click="showvideo = !showvideo">{{
            showvideo === true ? '隐藏视频' : '显示视频'
          }}</a-button>
          <el-pagination
            v-model:current-page="current1"
            v-model:page-size="pageSize3"
            show-quick-jumper
            :page-sizes="[1, 2, 4, 10, 50, 62, 100, 200, 300, 400, 2000]"
            layout="sizes, prev, pager, next, jumper"
            :total="pagetotal"
            @size-change="getData"
            @current-change="getData"
          />
        </div>
        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <div v-html="item.content"></div>
            <div v-for="(childItem, index) in item.children" :key="index">
              <div class="item">
                <div v-html="childItem.content"></div>
                <a-row>
                  <a-col :span="24">
                    <p class="an_a" v-html="childItem.answerone"></p>
                  </a-col>
                  <a-col :span="24"><p class="an_b" v-html="childItem.answertwo"></p> </a-col>
                  <a-col :span="24"><p class="an_c" v-html="childItem.answerthree"></p> </a-col>
                  <a-col :span="24"><p class="an_d" v-html="childItem.answerfour"></p> </a-col>
                </a-row>
              </div>
              <p @click="toggleTop">====================================</p>
              <div v-show="showContent" class="answer">
                <div v-html="childItem.answer"></div>
                <div>{{ childItem.source }}{{ childItem.createdTime }}</div>
                <div v-html="childItem.solution"></div>
                <dp1 v-if="showvideo" :url="childItem.id" :uid="childItem.id"></dp1>

                <p>====================================</p>
              </div>
            </div>
            <p>========================================</p>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { isNavigationFailure, useRoute } from 'vue-router';

  const data = ref([]);
  const isLoading = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const showTop = ref(true);
  const total = ref(1030);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const showvideo = ref(false);
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const increaseFontSize = () => {
    fontStyle.value += 2;
    updateFontSize();
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      if (current1.value > 1) {
        current1.value -= 1;
        getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);

          showContent.value = false;
        }
      }
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      if (current1.value < 10000) {
        current1.value += 1;
        getData();
        if (showContent.value) {
          // ansblack();
          isDanger(false);

          showContent.value = false;
        }
      }
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = (event) => {
    if (
      event.key === ' ' ||
      event.key === 'Spacebar' ||
      event.key === 'w' ||
      event.key === 'ArrowUp' ||
      event.key === 'ArrowDown'
    ) {
      if (showContent.value) {
        // ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        // ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = () => {
    let clen = 0;
    for (let i = 0; i < data.value.length; i++) {
      for (let j = 0; j < data.value[i].children.length; j++) {
        console.log(data.value[i].children[j].answer);
        console.log(clen);
        if (data.value && data.value[i].children[j].answer === 'A') {
          document.getElementsByClassName('an_a')[clen].style.color = 'red';
        }
        if (data.value && data.value[i].children[j].answer === 'B') {
          document.getElementsByClassName('an_b')[clen].style.color = 'red';
        }
        if (data.value && data.value[i].children[j].answer === 'C') {
          document.getElementsByClassName('an_c')[clen].style.color = 'red';
        }
        if (data.value && data.value[i].children[j].answer === 'D') {
          document.getElementsByClassName('an_d')[clen].style.color = 'red';
        }
        clen = clen + 1;
      }
    }
  };

  const ansblack = () => {
    let clen = 0;
    for (let i = 0; i < data.value.length; i++) {
      for (let j = 0; j < data.value[i].children.length; j++) {
        console.log(data.value[i].children[j].answer);
        console.log(clen);
        if (data.value && data.value[i].children[j].answer === 'A') {
          document.getElementsByClassName('an_a')[clen].style.color = 'black';
        }
        if (data.value && data.value[i].children[j].answer === 'B') {
          document.getElementsByClassName('an_b')[clen].style.color = 'black';
        }
        if (data.value && data.value[i].children[j].answer === 'C') {
          document.getElementsByClassName('an_c')[clen].style.color = 'black';
        }
        if (data.value && data.value[i].children[j].answer === 'D') {
          document.getElementsByClassName('an_d')[clen].style.color = 'black';
        }
        clen = clen + 1;
      }
    }
  };

  const togglean = (answer) => {
    if (showContent.value) {
      ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
    // console.log(response);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.referenceAnswer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };

  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'gwy';
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbzhiliao';
    let params = {
      per: per,
      page: page,
      id: id,
      type: type,
    };
    console.log(params);
    await ansblack();
    try {
      const response = await axios.get(url, { params });
      // if (response.data.length === 0) {
      //   await getData();
      //   return;
      // }
      if (a) {
        showContent.value = true;
      }

      data.value = response.data;
      // console.log(data.value);
      console.log(data.value[0].total);
      pagetotal.value = data.value[0].total;
      for (let item in data.value) {
        // console.log(data.value[item]);
        // let n = +item + 1;
        // data.value[item].content = data.value[item].content.replace(
        //   '<p>',
        //   '<p>' + n + '.',
        // );

        function isString(value) {
          return typeof value === 'string';
        }

        for (let x in data.value[item].children) {
          // data.value[item].children[x].content = data.value[item].children[
          //   x
          // ].content.replace('<p>', '<p>' + (+x + 1) + '.');
          // data.value[item].children[x].answerone =
          //   'A.' + data.value[item].children[x].answerone;
          // data.value[item].children[x].answertwo =
          //   'B.' + data.value[item].children[x].answertwo;
          // data.value[item].children[x].answerthree =
          //   'C.' + data.value[item].children[x].answerthree;
          // data.value[item].children[x].answerfour =
          //   'D.' + data.value[item].children[x].answerfour;

          try {
            if (
              isString(data.value[item].children[x].answerone) &&
              isString(data.value[item].children[x].answertwo) &&
              isString(data.value[item].children[x].answerthree) &&
              isString(data.value[item].children[x].answerfour)
            ) {
              data.value[item].children[x].content = isString(data.value[item].children[x].content)
                ? data.value[item].children[x].content.match(/<p>/g)
                  ? data.value[item].children[x].content.replace('<p>', `<p>${+x + 1}.`)
                  : `${+x + 1}.` + data.value[item].children[x].content
                : data.value[item].children[x].content;
              data.value[item].children[x].answerone = data.value[item].children[x].answerone.match(
                /<p>/g,
              )
                ? data.value[item].children[x].answerone.replace('<p>', '<p>A.')
                : 'A.' + data.value[item].children[x].answerone;
              data.value[item].children[x].answertwo = data.value[item].children[x].answertwo.match(
                /<p>/g,
              )
                ? data.value[item].children[x].answertwo.replace('<p>', '<p>B.')
                : 'B.' + data.value[item].children[x].answertwo;
              data.value[item].children[x].answerthree = data.value[item].children[
                x
              ].answerthree.match(/<p>/g)
                ? data.value[item].children[x].answerthree.replace('<p>', '<p>C.')
                : 'C.' + data.value[item].children[x].answerthree;
              data.value[item].children[x].answerfour = data.value[item].children[
                x
              ].answerfour.match(/<p>/g)
                ? data.value[item].children[x].answerfour.replace('<p>', '<p>D.')
                : 'D.' + data.value[item].children[x].answerfour;
            }
          } catch (error) {
            console.error('Error:', error);
          }
        }
      }
      // ansblack();
      // isDanger(false);

      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
