/**
 * 🎨 画布Socket.IO连接管理器
 * 基于增量传输的实时画布协作系统
 */

import io from 'socket.io-client';

class CanvasSocketManager {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.currentRoomId = null;
    this.userId = this.generateUserId();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.messageQueue = []; // 离线消息队列
    this.eventHandlers = new Map(); // 事件处理器
    
    // 绑定方法到实例
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.joinRoom = this.joinRoom.bind(this);
    this.sendCanvasUpdate = this.sendCanvasUpdate.bind(this);
  }

  /**
   * 🔗 连接到Socket.IO服务器
   */
  async connect() {
    if (this.socket && this.isConnected) {
      console.log('🔗 Socket.IO已连接，跳过重复连接');
      return;
    }

    try {
      console.log('🔗 正在连接画布Socket.IO服务器...');
      
      // 创建Socket.IO连接
      this.socket = io('/canvas', {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxHttpBufferSize: 1e8, // 100MB
        pingTimeout: 60000,
        pingInterval: 25000
      });

      // 设置事件监听器
      this.setupEventListeners();

    } catch (error) {
      console.error('❌ Socket.IO连接失败:', error);
      throw error;
    }
  }

  /**
   * 🎧 设置事件监听器
   */
  setupEventListeners() {
    // 连接成功
    this.socket.on('connect', () => {
      console.log('✅ 画布Socket.IO连接成功:', this.socket.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // 发送队列中的消息
      this.flushMessageQueue();
      
      // 触发连接成功事件
      this.emit('connected', { socketId: this.socket.id });
    });

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('❌ 画布Socket.IO连接断开:', reason);
      this.isConnected = false;
      this.emit('disconnected', { reason });
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket.IO连接错误:', error);
      this.reconnectAttempts++;
      this.emit('connect_error', { error, attempts: this.reconnectAttempts });
    });

    // 服务器确认连接
    this.socket.on('connected', (data) => {
      console.log('🎨 服务器确认连接:', data);
      this.emit('server_connected', data);
    });

    // 房间加入成功
    this.socket.on('room_joined', (data) => {
      console.log('🏠 成功加入房间:', data);
      this.currentRoomId = data.roomId;
      this.emit('room_joined', data);
    });

    // 用户加入房间
    this.socket.on('user_joined', (data) => {
      console.log('👤 用户加入房间:', data);
      this.emit('user_joined', data);
    });

    // 用户离开房间
    this.socket.on('user_left', (data) => {
      console.log('👋 用户离开房间:', data);
      this.emit('user_left', data);
    });

    // 接收画布更新
    this.socket.on('canvas_update', (data) => {
      console.log('🎨 接收画布更新:', data);
      this.emit('canvas_update', data);
    });

    // 接收擦除操作
    this.socket.on('canvas_erase', (data) => {
      console.log('🗑️ 接收擦除操作:', data);
      this.emit('canvas_erase', data);
    });

    // 完整同步
    this.socket.on('full_sync', (data) => {
      console.log('🔄 接收完整同步:', data);
      this.emit('full_sync', data);
    });

    // 更新确认
    this.socket.on('update_confirmed', (data) => {
      console.log('✅ 更新确认:', data);
      this.emit('update_confirmed', data);
    });

    // 房间状态
    this.socket.on('room_status', (data) => {
      console.log('📊 房间状态:', data);
      this.emit('room_status', data);
    });

    // 心跳响应
    this.socket.on('pong', (data) => {
      console.log('💓 心跳响应:', data);
      this.emit('pong', data);
    });

    // 错误处理
    this.socket.on('error', (error) => {
      console.error('❌ Socket.IO错误:', error);
      this.emit('error', error);
    });
  }

  /**
   * 🏠 加入房间
   */
  async joinRoom(roomId, userInfo = {}) {
    if (!this.isConnected) {
      console.warn('⚠️ Socket.IO未连接，将消息加入队列');
      this.messageQueue.push({ type: 'join_room', data: { roomId, userId: this.userId, userInfo } });
      return;
    }

    console.log(`🏠 加入房间: ${roomId}`);
    this.socket.emit('join_room', {
      roomId: roomId,
      userId: this.userId,
      userInfo: {
        name: userInfo.name || '匿名用户',
        avatar: userInfo.avatar || '',
        device: this.getDeviceInfo(),
        ...userInfo
      }
    });
  }

  /**
   * 🚪 离开房间
   */
  async leaveRoom() {
    if (!this.isConnected || !this.currentRoomId) {
      return;
    }

    console.log(`🚪 离开房间: ${this.currentRoomId}`);
    this.socket.emit('leave_room');
    this.currentRoomId = null;
  }

  /**
   * 🎨 发送画布更新
   */
  async sendCanvasUpdate(strokes, metadata = {}) {
    if (!this.isConnected) {
      console.warn('⚠️ Socket.IO未连接，将消息加入队列');
      this.messageQueue.push({ 
        type: 'canvas_update', 
        data: { strokes, userId: this.userId, metadata, timestamp: Date.now() }
      });
      return;
    }

    if (!this.currentRoomId) {
      console.warn('⚠️ 未加入任何房间，无法发送画布更新');
      return;
    }

    console.log(`🎨 发送画布更新: ${strokes.length} 个笔画`);
    this.socket.emit('canvas_update', {
      strokes: strokes,
      userId: this.userId,
      metadata: metadata,
      timestamp: Date.now()
    });
  }

  /**
   * 🗑️ 发送擦除操作
   */
  async sendCanvasErase(eraseArea, eraseType = 'area') {
    if (!this.isConnected || !this.currentRoomId) {
      return;
    }

    console.log('🗑️ 发送擦除操作');
    this.socket.emit('canvas_erase', {
      eraseArea: eraseArea,
      eraseType: eraseType,
      userId: this.userId,
      timestamp: Date.now()
    });
  }

  /**
   * 🔄 请求完整同步
   */
  async requestFullSync() {
    if (!this.isConnected || !this.currentRoomId) {
      return;
    }

    console.log('🔄 请求完整同步');
    this.socket.emit('request_full_sync');
  }

  /**
   * 📊 获取房间状态
   */
  async getRoomStatus() {
    if (!this.isConnected || !this.currentRoomId) {
      return;
    }

    console.log('📊 获取房间状态');
    this.socket.emit('get_room_status');
  }

  /**
   * 💓 发送心跳
   */
  async ping(data = {}) {
    if (!this.isConnected) {
      return;
    }

    this.socket.emit('ping', {
      timestamp: Date.now(),
      ...data
    });
  }

  /**
   * 🔌 断开连接
   */
  disconnect() {
    if (this.socket) {
      console.log('🔌 断开Socket.IO连接');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.currentRoomId = null;
    }
  }

  /**
   * 📨 发送队列中的消息
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      
      switch (message.type) {
        case 'join_room':
          this.socket.emit('join_room', message.data);
          break;
        case 'canvas_update':
          this.socket.emit('canvas_update', message.data);
          break;
        default:
          console.warn('未知的消息类型:', message.type);
      }
    }
  }

  /**
   * 🎧 添加事件监听器
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event).add(handler);
  }

  /**
   * 🔇 移除事件监听器
   */
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).delete(handler);
    }
  }

  /**
   * 📢 触发事件
   */
  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * 🆔 生成用户ID
   */
  generateUserId() {
    const stored = localStorage.getItem('canvas_user_id');
    if (stored) {
      return stored;
    }
    
    const newId = 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    localStorage.setItem('canvas_user_id', newId);
    return newId;
  }

  /**
   * 📱 获取设备信息
   */
  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      touchPoints: navigator.maxTouchPoints || 0,
      pointerEvents: !!window.PointerEvent,
      timestamp: Date.now()
    };
  }

  /**
   * 📊 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      socketId: this.socket?.id,
      currentRoomId: this.currentRoomId,
      userId: this.userId,
      queueLength: this.messageQueue.length,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// 创建单例实例
const canvasSocketManager = new CanvasSocketManager();

export default canvasSocketManager;
