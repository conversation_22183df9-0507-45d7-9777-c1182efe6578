<script setup>
  import { onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const imglist = ref([]);
  const currentIndex = ref(1);
  const pageSize3 = ref(10);
  const total = ref(7620);

  // 预加载图片
  const preloadImages = (images) => {
    images.forEach((src) => {
      const img = new Image();
      img.src = src; // 浏览器会预先加载这些图片资源
    });
  };

  // 获取数据并预加载图片
  const getData = async () => {
    // const page = route.query.page || currentIndex.value;
    // const url = '/egghk/xrpluslist';
    // let params = { page };
    // try {
    //   const response = await axios.get(url, { params });
    //   if (!response.data || response.data.length === 0) {
    //     console.error('No data received. Please check the server response.');
    //     return;
    //   }
    //   imglist.value = response.data; // 更新图片列表
    //   for (let item of imglist.value) {
    //     item.url = item.url.replace('/xrplusnei', '/fb/xr');
    //   }
    //   preloadImages(imglist.value); // 预加载图片
    // } catch (error) {
    //   console.error('Error fetching data:', error);
    // }
  };

  // 显示上一张图片
  const showPrev = async () => {
    currentIndex.value = (currentIndex.value - 1 + imglist.value.length) % imglist.value.length;
    await getData();
  };

  // 显示下一张图片
  const showNext = async () => {
    currentIndex.value = (currentIndex.value + 1) % imglist.value.length;
    await getData();
  };

  onMounted(async () => {
    await getData();
  });
</script>

<template>
  <a-row type="flex">
    <!-- 左边按钮 -->
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <!--      <div class="lb">上一张</div>-->
    </a-col>

    <!-- 图片显示区域 -->
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <el-pagination
        v-model:current-page="currentIndex"
        v-model:page-size="pageSize3"
        layout="prev, pager, next"
        :total="total"
        @size-change="getData"
        @current-change="getData"
      />
      <div class="wp">
        <div v-for="(item, index) in imglist" class="full-width-image">
          <a :href="item.url" target="_blank"><img :src="item.refm" /> </a>
        </div>
      </div>
      <el-pagination
        v-model:current-page="currentIndex"
        v-model:page-size="pageSize3"
        layout="prev, pager, next"
        :total="total"
        @size-change="getData"
        @current-change="getData"
      />
    </a-col>

    <!-- 右边按钮 -->
    <a-col :lg="5" :sm="1" :xs="1" :md="1" @click="showNext">
      <!--      <div class="rb">下一张</div>-->
    </a-col>
  </a-row>
</template>

<style scoped>
  * {
    touch-action: manipulation;
  }
  /* 确保整个页面的溢出被隐藏 */
  img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }
</style>
