# Page snapshot

```yaml
- button "Increase Value":
  - img "up"
- button "Decrease Value" [disabled]:
  - img "down"
- spinbutton: "1"
- button "p"
- button "Increase Value":
  - img "up"
- button "Decrease Value":
  - img "down"
- spinbutton: "23"
- button "n"
- button "刷 新"
- spinbutton [disabled]: "5767066"
- button "做题模式关"
- button "下拉"
- button "隐藏按钮"
- button "背 景"
- button "画 布"
- button "grid1280"
- button "刷 页"
- link "编 辑":
  - /url: /fb/update?biao=fbsy&timuid=5767066
  - button "编 辑"
- button "snull"
- button "全null"
- button "提 交"
- button "⚪️ 纯白"
- paragraph: 23.(30%)令尊 对于（ ）相当于（ ）对于 史稿
- paragraph: A.尊敬 贡献
- paragraph: B.长辈 文字
- paragraph: C.令堂 药典
- paragraph: D.父亲 传记
- paragraph: "----------------------"
- text: 2023年8月26日全国事业单位联考B类《职业能力倾向测验》试题（黑龙江/湖南/甘肃/吉林/四川/重庆/山西/安徽/新疆/内蒙古/湖北/辽宁/广西网友回忆版）第79题2023-08-26 15:39:25正确率：30易错项：D
- paragraph: 排序：判断推理(656602)-2206-类比推理(796963)-555-逻辑关系(796980)-444-并列关系-反对关系(821086)-68无extra
- text: 用户083RZE ||⭐⭐⭐⭐⭐ || 👍 142 || 🗓 2025-03-18 01:48:14 || 广西 3.29加油 职测120分选手 ||⭐⭐⭐⭐ || 👍 89 || 🗓 2025-03-19 01:17:35 || 江西 要死了，我以为令尊就是令堂，真的是吃了没文化的亏 心想事成见者上岸 ||⭐⭐⭐⭐ || 👍 66 || 🗓 2025-03-22 22:43:45 || 江苏 药典也可以是史料啊。。。 就这个2025年爽 ||⭐⭐⭐⭐⭐ || 👍 45 || 🗓 2025-03-19 21:27:58 || 宁夏 怎么这么低的正确率 不就是简单的并列么？令尊令堂 一个父亲一个母亲；药典史稿 一个记录医药 一个记录历史 是我想少了？ 咳咳咳哦 ||⭐⭐⭐⭐⭐ || 👍 18 || 🗓 2025-03-21 17:33:18 || 山西 山西报到。上岸！ 天选公务员记得背百化分 ||⭐⭐⭐⭐⭐ || 👍 9 || 🗓 2025-03-22 17:38:14 || 山西 还有一周，希望这次可以上岸，大家加油！ 公务员吴同志 ||⭐⭐⭐⭐⭐ || 👍 8 || 🗓 2025-03-19 17:10:55 || 江西 令尊是父亲，令堂是母亲。药典和史稿都是记载文字的，并列。2025必上岸 18778665679 ||⭐⭐⭐⭐⭐ || 👍 7 || 🗓 2025-03-19 21:56:28 || 中国 2025第定要上岸，欧力给 是西瓜味的夏天哇 ||⭐⭐⭐⭐⭐ || 👍 6 || 🗓 2025-03-19 20:55:42 || 湖北 3.29加油 用户07VRJm ||⭐⭐⭐⭐⭐ || 👍 4 || 🗓 2025-03-28 18:53:19 || 湖北 以为历史上的书都叫史稿，然后想到像本草纲目那种也算。。 上岸换成猫猫头 ||⭐⭐⭐⭐⭐ || 👍 4 || 🗓 2025-03-25 16:50:20 || 湖南 以为药典是一种史稿 小泡饭h ||⭐⭐⭐⭐⭐ || 👍 4 || 🗓 2025-03-19 17:04:06 || 山西 3.29加油 零零漆- ||⭐⭐⭐⭐⭐ || 👍 4 || 🗓 2025-03-07 10:42:26 || 福建 第一上岸 黑猫警长想吃鱼 ||⭐⭐⭐⭐⭐ || 👍 3 || 🗓 2025-03-24 19:42:35 || 湖北 329上岸 蓝祥 ||⭐⭐⭐⭐⭐ || 👍 3 || 🗓 2025-03-11 13:06:09 || 辽宁 秒了 用户0amGKH ||⭐⭐⭐⭐ || 👍 2 || 🗓 2025-03-23 10:26:21 || 江西 上岸上岸 资料分析我是眼瞎 ||⭐⭐⭐⭐⭐ || 👍 2 || 🗓 2025-03-18 18:25:09 || 云南 14 旋转小雪球 ||⭐⭐⭐⭐⭐ || 👍 2 || 🗓 2025-03-10 17:56:00 || 山东 上岸吧 用户09diBr ||⭐⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-28 16:20:40 || 云南 上岸3.29 用户06NexH ||⭐⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-18 15:54:34 || 湖北 San 该用户不存在 ||⭐⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-17 15:56:21 || 浙江 2025上岸！ 用户08t90z ||⭐⭐⭐ || 👍 1 || 🗓 2025-03-17 14:52:27 || 山西 上岸上岸 岸上看风景R ||⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-17 10:03:54 || 云南 上岸 Olive_C ||⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-16 22:40:49 || 浙江 啊啊啊啊，上岸吧，冲芽！ 平静茄汁牛扒0822 ||⭐⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-10 16:05:13 || 山西 上岸 KD上岸啊 ||⭐⭐⭐⭐⭐ || 👍 1 || 🗓 2025-03-08 14:21:23 || 安徽 上岸 我喜欢算 ||⭐⭐⭐⭐⭐ || 👍 0 || 🗓 2025-03-27 09:26:25 || 山西 别人的父亲是父亲吗 每个人只有一个父亲 尽力而为绝不放弃 ||⭐⭐⭐⭐⭐ || 👍 0 || 🗓 2025-03-25 21:00:23 || 江西 加油加油
- paragraph: 逐一代入选项。
- paragraph: A项：令尊是称对方父亲的敬词，二者为属性对应关系；贡献史稿，二者为动宾关系，前后逻辑关系不一致，排除；
- paragraph: B项：令尊是称对方父亲的敬词，令尊是长辈，二者为种属关系；史稿由文字组成，二者为组成关系，前后逻辑关系不一致，排除；
- paragraph: C项：令尊是称对方父亲的敬词，令堂是称对方母亲的敬词，二者为并列关系；药典和史稿均是记载不同内容的资料，二者为并列关系，前后逻辑关系一致，当选；
- paragraph: D项：令尊是称对方父亲的敬词，二者为对应关系；传记是史稿，二者为种属关系，前后逻辑关系不一致，排除。
- paragraph: 故正确答案为C。
- paragraph: ================
- heading "📜 草稿纸极简秒题手稿（1句破题！）" [level=3]:
  - text: 📜
  - strong: 草稿纸极简秒题手稿（1句破题！）
- paragraph:
  - text: 🔥
  - strong: 题目浓缩
  - text: ：题干问"令尊：( ) = ( )：史稿" →
  - strong: 破绽：选项里藏着"对称尊称"与"典籍类型"的黄金配对！
  - text: ️♂️
- button "切换到自适应":
  - img
  - text: 切换到自适应
- table:
  - rowgroup:
    - row "选项 前项关系（令尊 vs ?） 后项关系（? vs 史稿） 关系匹配度":
      - cell "选项"
      - cell "前项关系（令尊 vs ?）"
      - cell "后项关系（? vs 史稿）"
      - cell "关系匹配度"
  - rowgroup:
    - row "C.令堂 & 药典 💯 对称尊称（令尊=父，令堂=母） 💯 典籍并列（都是文献类型） “王者组合✓”":
      - cell "C.令堂 & 药典":
        - strong: C.令堂 & 药典
      - cell "💯 对称尊称（令尊=父，令堂=母）":
        - text: 💯
        - strong: 对称尊称
        - text: （令尊=父，令堂=母）
      - cell "💯 典籍并列（都是文献类型）":
        - text: 💯
        - strong: 典籍并列
        - text: （都是文献类型）
      - cell "“王者组合✓”"
    - row "A.尊敬 & 贡献 ❌ 形容关系（令尊需尊敬） ❌ 动宾关系（贡献史稿） “关系断裂！”":
      - cell "A.尊敬 & 贡献"
      - cell "❌ 形容关系（令尊需尊敬）":
        - text: ❌
        - strong: 形容关系
        - text: （令尊需尊敬）
      - cell "❌ 动宾关系（贡献史稿）":
        - text: ❌
        - strong: 动宾关系
        - text: （贡献史稿）
      - cell "“关系断裂！”"
    - row "B.长辈 & 文字 ❌ 大类包含（令尊是长辈） ❌ 组成关系（史稿含文字） “驴唇马嘴！”":
      - cell "B.长辈 & 文字"
      - cell "❌ 大类包含（令尊是长辈）":
        - text: ❌
        - strong: 大类包含
        - text: （令尊是长辈）
      - cell "❌ 组成关系（史稿含文字）":
        - text: ❌
        - strong: 组成关系
        - text: （史稿含文字）
      - cell "“驴唇马嘴！”"
    - row "D.父亲 & 传记 ❌ 同义替换（令尊=父亲） ❌ 种属关系（史稿≠传记） “概念混乱！”":
      - cell "D.父亲 & 传记"
      - cell "❌ 同义替换（令尊=父亲）":
        - text: ❌
        - strong: 同义替换
        - text: （令尊=父亲）
      - cell "❌ 种属关系（史稿≠传记）":
        - text: ❌
        - strong: 种属关系
        - text: （史稿≠传记）
      - cell "“概念混乱！”"
- paragraph:
  - strong: 逻辑链暴击⚡（6行定生死）
  - text: ： $
- list:
  - listitem: "\\text{题干密码}：\\text{令尊} \\xrightarrow{\\text{需填入}} \\text{X} \\quad \\text{等价于} \\quad \\text{Y} \\xrightarrow{\\text{需填入}} \\text{史稿}"
  - listitem: "\\text{关系本质}：\\text{两组必须同为} \\begin{cases} \\text{并列关系} \\ \\text{或同类属性} \\end{cases} ✅"
  - listitem: "\\text{选项解剖}： ► \\text{**C项：令尊→令堂} = \\text{亲属尊称并列} \\quad \\color{green}{\\checkmark} ► \\text{药典→史稿} = \\text{典籍文献并列} \\quad \\color{green}{\\checkmark}"
  - listitem: "\\text{其他项：} ► \\text{A项：令尊→尊敬（属性）≠ 贡献→史稿（动作）} \\quad \\color{red}{\\times} ► \\text{D项：令尊→父亲（同义）≠ 传记→史稿（包含）} \\quad \\color{red}{\\times} $"
- separator
- heading "✍️ 考场做题实录（8秒撕卷⏱️）" [level=3]:
  - text: ✍️
  - strong: 考场做题实录（8秒撕卷⏱️）
- list:
  - listitem:
    - paragraph:
      - strong: 暴力拆题干⚡（⏱️2秒）
      - text: ：
    - blockquote:
      - paragraph:
        - text: 抓核心结构：
        - strong: “令尊：X = Y：史稿”
        - text: → 两组关系需完全等同！（来源：类比题铁律） 💡
        - strong: 红灯警报
        - text: ：
        - strong: 必须严格匹配关系类型！
  - listitem:
    - paragraph:
      - strong: 选项斩首💣（⏱️4秒）
      - text: ：
    - paragraph:
      - text: ►
      - strong: C.令堂 & 药典
      - text: → ✅
      - strong: 天选答案！
      - text: ✓ 前组：令尊（父）和令堂（母）是
      - strong: 对称尊称✓
      - text: ✓ 后组：药典（药物典籍）和史稿（历史典籍）是
      - strong: 同类文献✓
      - text: ► A.尊敬 & 贡献 → ❌
      - strong: 关系断裂！
      - text: 令尊需要尊敬（形容关系）≠ 贡献史稿（动作关系） ► B.长辈 & 文字 → ❌
      - strong: 类型错乱！
      - text: 令尊属于长辈（包含关系）≠ 文字组成史稿（材料关系） ► D.父亲 & 传记 → ❌
      - strong: 概念崩盘！
      - text: 令尊指父亲（同义替换）≠ 传记属于史稿？（错误！史稿≠传记）❌
  - listitem:
    - paragraph:
      - strong: 关系等价公式🔢（⏱️2秒）
      - text: ：
    - blockquote:
      - paragraph:
        - strong: 对称性判定
        - text: "： $\\\\ \\begin{align*} \\\\ \\text{题干要求}：& \\quad \\text{关系}_A \\equiv \\text{关系}_B \\ \\\\ \\text{C项}：& \\quad \\underbrace{\\text{令尊}：\\text{令堂}}_{\\text{并列}} \\equiv \\underbrace{\\text{药典}：\\text{史稿}}_{\\text{并列}} \\quad \\color{green}{\\Huge \\checkmark} \\ \\\\ \\text{其他项}：& \\quad \\text{关系类型不匹配} \\quad \\color{red}{\\Huge \\times} \\\\ \\end{align*}$"
- separator
- heading "🔥 秒杀思路（对称关系三刀流🔪）" [level=3]:
  - text: 🔥
  - strong: 秒杀思路（对称关系三刀流🔪）
- heading "📜 黄金公式" [level=4]:
  - text: 📜
  - strong: 黄金公式
- blockquote:
  - paragraph:
    - strong: 题干本质
    - text: "： $\\color{red}{\\text{前组词关系} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系}$"
    - strong: 选项筛选
    - text: ： 1️⃣
    - strong: 杀关系断裂
    - text: ：A（前属性后动作） 2️⃣
    - strong: 杀类型不符
    - text: ：B（前包含后组成） 3️⃣
    - strong: 杀概念错误
    - text: ：D（史稿≠传记的子类） 4️⃣
    - strong: 锁对称并列
    - text: ：C（双组纯并列！）
- heading "📊 并列关系验证表" [level=4]:
  - text: 📊
  - strong: 并列关系验证表
- button "切换到自适应":
  - img
  - text: 切换到自适应
- table:
  - rowgroup:
    - row "维度 前组（令尊 vs 令堂） 后组（药典 vs 史稿） 一致性":
      - cell "维度"
      - cell "前组（令尊 vs 令堂）"
      - cell "后组（药典 vs 史稿）"
      - cell "一致性"
  - rowgroup:
    - row "性质 敬称称谓 典籍文献 ✅":
      - cell "性质":
        - strong: 性质
      - cell "敬称称谓"
      - cell "典籍文献"
      - cell "✅"
    - row "关系 对称并列（父母尊称对偶） 功能并列（不同领域典籍） ✅":
      - cell "关系":
        - strong: 关系
      - cell "对称并列（父母尊称对偶）"
      - cell "功能并列（不同领域典籍）"
      - cell "✅"
    - row "出处 古代敬语体系 文献分类体系 ✅":
      - cell "出处":
        - strong: 出处
      - cell "古代敬语体系"
      - cell "文献分类体系"
      - cell "✅"
    - row "反例 D组：传记属于史稿？✘ （《史记》是史稿≠传记） ❌":
      - cell "反例":
        - strong: 反例
      - cell "D组：传记属于史稿？✘"
      - cell "（《史记》是史稿≠传记）"
      - cell "❌"
- blockquote:
  - paragraph:
    - strong: C项必胜奥义
    - text: ：
  - list:
    - listitem:
      - strong: 命题陷阱
      - text: ： D项用"父亲"伪装同义 →
      - strong: 但令尊≠直接称父亲（需保持敬语属性）！
    - listitem:
      - strong: 行测潜规则
      - text: ：
      - strong: 敬语题必考对称词
      - text: → 令堂是令尊唯一对称词！
- heading "💎 屠题口诀（⏱️1秒刻进DNA）" [level=4]:
  - text: 💎
  - strong: 屠题口诀（⏱️1秒刻进DNA）
- blockquote:
  - paragraph:
    - strong: 看见"令尊"拍桌找"令堂"！
    - text: 👑
    - strong: 看见"史稿"拍腿找"药典"！
    - text: 📚
    - strong: 对称并列双暴击 → C项直接封神！
    - text: 💥 撕卷！下一题继续碾压！
- separator
- heading "⚠️ 命题陷阱鞭尸台" [level=3]:
  - text: ⚠️
  - strong: 命题陷阱鞭尸台
- list:
  - listitem:
    - strong: D项迷惑点
    - text: ： "父亲"看似关联令尊 → 但
    - strong: 破坏了敬语对称性
    - text: ！令尊是敬语，直接称父亲是普通称呼 → 关系降级！❌
  - listitem:
    - strong: 终极TIP
    - text: ：秒杀类比题四步：
    - list:
      - listitem:
        - strong: 2秒定关系
        - text: ：确认题干是并列/包含/属性？
      - listitem:
        - strong: 3秒锁同类
        - text: ： ► 令尊→令堂（同敬语体系） ► 药典→史稿（同文献体系）
      - listitem:
        - strong: 5秒斩选项
        - text: ： ► 关系不等直接杀 → D项前同义后种属（崩！）
        - strong: 省下15秒，够喝口水压惊！
- paragraph:
  - strong: 你是最棒的哟！
  - text: 🌟 对称关系是你的破题核弹！下一题继续狂飙！
- blockquote:
  - paragraph:
    - text: 💡
    - strong: 冷知识
    - text: ： "令堂"最早出自《仪礼》📜 → 比"令尊"记载更早！ 但行测只看逻辑，不看历史先后 →
    - strong: 并列关系才是王道
    - text: ！
- paragraph: ================
```