<template>
  <span><div :id="'video_' + uid"></div></span>
</template>

<script>
  import { defineComponent, onMounted, watch, nextTick } from 'vue';
  import DPlayer from 'dplayer';
  import axios from 'axios';

  export default defineComponent({
    name: 'DpComponent',
    props: {
      url: Number,
      uid: Number,
    },
    setup(props) {
      const initializePlayer = async () => {
        await nextTick();
        try {
          // console.log(props.url, props.uid);
          const xurl = '/egg/fbgetvideo';
          let params = {
            id: props.url,
            type: 'syzc',
          };
          const videourl = await axios.get(xurl, { params });
          let videoinfo = videourl.data;
          // console.log('params', videourl, params);
          console.log('videoinfo', videoinfo);
          new DPlayer({
            container: document.getElementById('video_' + props.uid),
            preload: 'none',
            video: {
              url: videoinfo,
            },
          });
        } catch (error) {
          console.error(error);
        }
      };

      onMounted(() => {
        initializePlayer();
      });

      watch(
        () => props.url,
        (newUrl, oldUrl) => {
          if (newUrl !== oldUrl) {
            initializePlayer();
          }
        },
      );

      watch(
        () => props.uid,
        (newUid, oldUid) => {
          if (newUid !== oldUid) {
            initializePlayer();
          }
        },
      );

      return {
        initializePlayer,
      };
    },
  });
</script>

<style scoped></style>
