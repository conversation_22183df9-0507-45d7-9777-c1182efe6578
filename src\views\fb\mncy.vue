<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="left">
      <div class="lb" style=""></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <a-input-number id="inputNumber" v-model:value="perpage" :min="1" @change="getData" />
      <a-button @click="left">p</a-button>
      <a-input-number id="inputNumber" v-model:value="current_page" :min="1" @change="getData" />
      <a-button @click="right">n</a-button>
      <a-collapse v-model:active-key="activeKey" accordion>
        <template v-for="(cy, index) in data" :key="index">
          <a-collapse-panel @click="getTimu(cy.timus, cy.name)">
            <template #header>
              <span style="color: red; font-size: 18px"
                >{{ (current_page - 1) * perpage + index + 1 }}.{{ cy.name }}</span
              >
              <p style="color: black; font-size: 18px">{{ cy.jieshi }} {{ cy.count }}</p>
            </template>
            <a-textarea
              v-if="!cy.jieshi"
              v-model:value="jieshitext"
              placeholder="Autosize height based on content lines"
              auto-size
              @change="postjieshi(jieshitext, cy.id)"
            />
            <div v-for="(item, index) in timusdata">
              <div
                class="maintimu"
                style="color: black; font-size: 18px"
                v-html="item.content"
              ></div>
              <div class="item" style="color: black; font-size: 18px">
                <p
                  class="an_a"
                  :style="{
                    color: item.choice === '0' ? 'red' : '',
                  }"
                  v-html="item.A"
                ></p>
                <p
                  class="an_b"
                  :style="{ color: item.choice === '1' ? 'red' : '' }"
                  v-html="item.B"
                ></p>
                <p
                  class="an_c"
                  :style="{ color: item.choice === '2' ? 'red' : '' }"
                  v-html="item.C"
                ></p>
                <p
                  class="an_d"
                  :style="{ color: item.choice === '3' ? 'red' : '' }"
                  v-html="item.D"
                ></p>
              </div>
              <p>================================</p>
              <div class="answer" style="color: red; font-size: 18px">
                <div class="ansinfo">{{ item.source }}{{ item.createdTime }}</div>
                <br />
                <div>
                  <div v-html="item.solution"></div>
                </div>
                <div></div>
                <p>==================</p>
              </div>
            </div>
          </a-collapse-panel>
        </template>
      </a-collapse>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="right">
      <div class="rb" style=""></div>
    </a-col>
  </a-row>
</template>
<script setup>
  import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  const activeKey = ref([]);
  const text = ref('wcnm');
  const data = ref([]);
  const timusdata = ref([]);
  const jieshitext = ref('');
  const current_page = ref(1);
  const perpage = ref(30);
  const postjieshi = async (text, id) => {
    const url = '/egg/fbgetmncy';
    text = `【解释】` + text;
    let params = {
      biao: 'fbmncy1',
      text: text,
      id: id,
      jiexi: '2',
    };
    try {
      const response = await axios.get(url, { params });
      console.log(response.data);
      console.log(text, id);
      jieshitext.value = null;
      await getData();
    } catch (error) {
      console.error(error);
    }
  };
  const open_count = ref(0);

  const updateValue = async (delta) => {
    open_count.value = 0;
    const newValue = +current_page.value + delta;
    if (newValue >= 1 && newValue <= 100000) {
      current_page.value = newValue;
      await getData();
    }
  };

  const right = async () => {
    await updateValue(1);
  };

  const left = async () => {
    await updateValue(-1);
  };
  const getTimu = async (timus, name) => {
    const url = '/egg/fbgetmncy';
    let params = {
      biao: 'fbmnkj',
      type: 'mnkj',
      timus: timus,
    };
    let x = [];
    try {
      const response = await axios.get(url, { params });
      timusdata.value = response.data;
      for (let item of timusdata.value) {
        if (item.A.match(name) && item.choice === '0') {
          x.push(item);
          console.log(item);
        } else if (item.B.match(name) && item.choice === '1') {
          x.push(item);
        } else if (item.C.match(name) && item.choice === '2') {
          x.push(item);
        } else if (item.D.match(name) && item.choice === '3') {
          x.push(item);
        }
      }
      timusdata.value = x;
      console.log(timusdata.value);
    } catch (error) {
      console.error(error);
    }
  };
  const getData = async () => {
    const url = '/egg/fbgetmncy';
    let params = {
      biao: 'fbmncy1',
      per: perpage.value,
      page: current_page.value,
    };
    console.log(params);
    try {
      const response = await axios.get(url, { params });
      data.value = response.data;
      console.log(data.value);
    } catch (error) {
      console.error(error);
    }
  };
  onMounted(async () => {
    await getData();
  });
</script>

<style scoped>
  /* For demo */
  :deep(.slick-slide) {
    text-align: center;
    height: 160px;
    line-height: 160px;
    background: #364d79;
    overflow: hidden;
  }

  :deep(.slick-arrow.custom-slick-arrow) {
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: #fff;
    background-color: rgba(31, 45, 61, 0.11);
    transition: ease all 0.3s;
    opacity: 0.3;
    z-index: 1;
  }
  :deep(.slick-arrow.custom-slick-arrow:before) {
    display: none;
  }
  :deep(.slick-arrow.custom-slick-arrow:hover) {
    color: #fff;
    opacity: 0.5;
  }

  :deep(.slick-slide h3) {
    color: #fff;
  }
</style>
