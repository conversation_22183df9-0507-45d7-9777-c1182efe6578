<template>
  <div class="theme-test-container">
    <h1>🎨 主题一致性测试页面</h1>

    <div class="info-section">
      <h2>当前页面信息</h2>
      <p><strong>页面ID:</strong> theme-test</p>
      <p><strong>当前主题:</strong> {{ themeStore.theme }}</p>
      <p><strong>sy 页面主题:</strong> {{ themeStore.syTheme }}</p>
      <p><strong>是否在 sy 页面:</strong> {{ themeStore.isSyPage ? '是' : '否' }}</p>
    </div>

    <div class="navigation-section">
      <h2>页面导航测试</h2>
      <div class="nav-buttons">
        <router-link to="/fb/sy?kjid=1_3e_269gah8&type=syzc&id=123" class="nav-btn">
          前往 SY 页面 (有主题切换)
        </router-link>
        <router-link to="/fb/nav3" class="nav-btn"> 前往 Nav3 页面 (强制白色主题) </router-link>
        <router-link to="/fb/nav1" class="nav-btn"> 前往 Nav1 页面 (强制白色主题) </router-link>
      </div>
    </div>

    <div class="test-instructions">
      <h2>测试说明</h2>
      <ol>
        <li>当前页面应该始终显示白色主题</li>
        <li>点击前往 SY 页面，在那里可以切换主题</li>
        <li>从 SY 页面切换回其他任何页面，都应该强制显示白色主题</li>
        <li>SY 页面的主题设置不应该影响其他页面</li>
      </ol>
    </div>

    <div class="theme-info">
      <h2>主题状态调试信息</h2>
      <pre>{{
        JSON.stringify(
          {
            theme: themeStore.theme,
            syTheme: themeStore.syTheme,
            isSyPage: themeStore.isSyPage,
            localStorage_syTheme: localStorage?.getItem('sy-theme') || 'null',
          },
          null,
          2,
        )
      }}</pre>
    </div>
  </div>
</template>

<script setup>
import { useThemeStore } from '@/store/theme';
import { onMounted } from 'vue';

const themeStore = useThemeStore();

onMounted(() => {
  // 🔧 简化：App.vue 会自动处理主题
  console.log('🧪 主题测试页面已加载，App.vue 会自动设置白色主题');
});
</script>

<style scoped>
.theme-test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.info-section,
.navigation-section,
.test-instructions,
.theme-info {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
}

.nav-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.nav-btn {
  display: inline-block;
  padding: 12px 20px;
  background: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.nav-btn:hover {
  background: #0056b3;
  color: white;
}

pre {
  background: #f1f3f4;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

h1 {
  color: #1a73e8;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #333;
  border-bottom: 2px solid #e1e5e9;
  padding-bottom: 8px;
}

ol li {
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
