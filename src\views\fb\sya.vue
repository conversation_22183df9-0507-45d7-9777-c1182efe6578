<template>
  <a-row>
    <a-col :lg="data.length === 2 ? 1 : 5" :sm="1" :xs="1" :md="1">
      <div class="lb" style="" @click="left"></div>
    </a-col>
    <a-col :lg="data.length === 2 ? 22 : 14" :sm="22" :xs="22" :md="22">
      <div class="wp" style="">
        <a-space :size="[8, 16]" wrap>
          <template v-for="(item, index) in data" :key="index">
            <div v-html="item.answer"></div>
          </template>
        </a-space>
        <p @click="toggleTop">====================================</p>
        <a-row>
          <a-col
            v-for="(item, index) in data"
            :key="index"
            :span="data.length === 2 ? 12 : 24"
            class="datalist"
          >
            <div v-if="isLoading">Loading...</div>
            <div v-if="!isLoading" :class="index === 1 ? 'items' : ''">
              <!--              <div>{{ resolution }}</div>-->
              <div v-html="item.answer"></div>
              <!-- <fbtag
                  :messageFromParent="item.id"
                  :messageFromParent1="typex"
                ></fbtag> -->
              <div class="item" @click="togglean">
                <p class="an_a" @click="pushans(item.id, 'A')" v-html="item.answerone"></p>
                <p class="an_b" @click="pushans(item.id, 'B')" v-html="item.answertwo"></p>
                <p class="an_c" @click="pushans(item.id, 'C')" v-html="item.answerthree"></p>
                <p class="an_d" @click="pushans(item.id, 'D')" v-html="item.answerfour"></p>
              </div>
              <p @click="toggleTop">====================================</p>
              <div v-show="showContent" class="answer">
                <!-- <div v-html="item.answer"></div> -->

                <div>
                  <span style="color: blue">{{ item.choice }}</span>
                  {{ item.source }}{{ item.createdTime }}
                </div>
                <br />
                <div v-html="item.solution"></div>
                <p>====================================</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-show="showTop" class="top">
        <div class="top-button">
          <a-button @click="increaseFontSize">下一页</a-button>
          <a-button class="showans" @click="togglean">显示答案</a-button>
          <!--          <a-button>{{ total }}</a-button>-->
          <a-button @click="toggleTop">隐藏顶部内容</a-button>
          <!--          <fbhiddentag></fbhiddentag>-->
          <a-button @click="toggleshowdaan">
            {{ showdaan === true ? '已' : '不' }}显示答案
          </a-button>
        </div>
        <div class="top-input">
          <a-input
            v-model:value="perpage"
            style="width: 60px; height: 32px"
            @change="getData"
          ></a-input>
        </div>
        <a-pagination
          v-model:current="current1"
          v-model:page-size="perpage"
          show-quick-jumper
          :page-size-options="[
            1, 2, 3, 5, 6, 10, 62, 100, 200, 300, 399, 402, 666, 999, 9701, 12000,
          ]"
          :total="total"
          @change="getData"
          @show-size-change="getData"
        />
      </div>
    </a-col>
    <a-col :lg="data.length === 2 ? 1 : 5" :sm="1" :xs="1" :md="1">
      <div class="rb" style="" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script>
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';

  export default {
    name: 'Min',
    components: {
      fbtag,
      fbhiddentag,
    },
    setup() {
      const zltimu = ref([]);
      const data = ref([]);
      const isLoading = ref(true);
      const showTop = ref(true);
      const current1 = ref(1);
      const showContent = ref(false);
      const pagetotal = ref(20);
      const perpage = ref(1);
      const total = ref(0);
      const isdangerarr = ref([false, false, false, false]);
      const route = useRoute();
      const fontStyle = ref(16);
      const isTagHidden = ref(false);
      const typex = ref('sy');
      const showdaan = ref(false);
      const resolution = ref('');

      const getResolution = () => {
        resolution.value = `${window.innerWidth} x ${window.innerHeight}`;
      };
      const toggleHiddenTag = () => {
        // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
        const elements = document.querySelectorAll('.tag');

        // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
        elements.forEach((element) => {
          if (isTagHidden.value) {
            element.style.display = 'block'; // 或者其他适当的显示样式
          } else {
            element.style.display = 'none';
          }
        });

        // 切换状态变量
        isTagHidden.value = !isTagHidden.value;
      };
      const toggleTop = () => {
        showTop.value = !showTop.value;
      };
      const increaseFontSize = () => {
        right();
        // fontStyle.value += 2;
        // updateFontSize();
      };
      const updateFontSize = () => {
        const wpElement = document.querySelector('.wp');
        wpElement.style.fontSize = `${fontStyle.value}px`;
      };

      const toggleshowdaan = () => {
        showdaan.value = !showdaan.value;
      };
      const handleKeyDown = async (event) => {
        if (event.key === 'ArrowLeft' || event.key === 'q') {
          if (current1.value > 1) {
            current1.value -= 1;
            await getData();
            if (!showdaan.value) {
              showContent.value = false;
            } else {
              showContent.value = true;
              await ansred();
            }
          }
        } else if (event.key === 'ArrowRight' || event.key === 'e') {
          if (current1.value < 10000) {
            current1.value += 1;
            await getData();
            if (!showdaan.value) {
              showContent.value = false;
            } else {
              showContent.value = true;
              await ansred();
            }
          }
        }
      };

      const right = () => {
        if (current1.value < 10000) {
          current1.value += 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };

      const left = () => {
        if (current1.value > 1) {
          current1.value -= 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };
      const toggleContent = async (event) => {
        if (
          event.key === ' ' ||
          event.key === 'ArrowUp' ||
          event.key === 'ArrowDown' ||
          event.key === 'Spacer' ||
          event.key === 'w' ||
          event.key === 'x'
        ) {
          if (showContent.value) {
            ansblack();
            isDanger(false);
            showContent.value = false;
          } else {
            await getData();
            await ansred();
            const showAnsButton = document.querySelector('.showans');
            showAnsButton.click();
            showContent.value = true;
          }
        }
      };

      const ansred = async () => {
        for (let i = 0; i < data.value.length; i++) {
          console.log(data.value);
          if (data.value && data.value[i].choice === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].choice === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'blue';
          }
          if (data.value && data.value[i].answer === 'A') {
            document.getElementsByClassName('an_a')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'B') {
            document.getElementsByClassName('an_b')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'C') {
            document.getElementsByClassName('an_c')[i].style.color = 'red';
          }
          if (data.value && data.value[i].answer === 'D') {
            document.getElementsByClassName('an_d')[i].style.color = 'red';
          }
        }
      };

      const ansblack = () => {
        for (let i = 0; i < data.value.length; i++) {
          document.getElementsByClassName('an_a')[i].style.color = 'black';
          document.getElementsByClassName('an_b')[i].style.color = 'black';
          document.getElementsByClassName('an_c')[i].style.color = 'black';
          document.getElementsByClassName('an_d')[i].style.color = 'black';
          // if (data.value && data.value[i].answer === 'A') {
          //   document.getElementsByClassName('an_a')[i].style.color = 'black';
          // }
          // if (data.value && data.value[i].answer === 'B') {
          //   document.getElementsByClassName('an_b')[i].style.color = 'black';
          // }
          // if (data.value && data.value[i].answer === 'C') {
          //   document.getElementsByClassName('an_c')[i].style.color = 'black';
          // }
          // if (data.value && data.value[i].answer === 'D') {
          //   document.getElementsByClassName('an_d')[i].style.color = 'black';
          // }
        }
      };

      const togglean = async (answer) => {
        if (showContent.value && !showdaan.value) {
          await ansblack();
          isDanger(false);
          showContent.value = false;
        } else {
          await getData();
          await ansred();
          isDanger(answer);
          showContent.value = true;
        }
      };

      const pushans = async (id, answer) => {
        const url = '/egg/fbchoice';
        const response = await axios.post(url, { id: id, choice: answer });
        console.log(response);
        if (response.data.affectedRows !== 0) {
          message.success({
            content: `选择${answer}成功`,
            style: {
              marginTop: '80vh',
            },
          });
        }
      };

      const isDanger = (answer) => {
        if (data.value && data.value.answer === answer && answer !== false) {
          switch (answer) {
            case 'A':
              isdangerarr.value[0] = true;
              break;
            case 'B':
              isdangerarr.value[1] = true;
              break;
            case 'C':
              isdangerarr.value[2] = true;
              break;
            case 'D':
              isdangerarr.value[3] = true;
              break;
          }
          return true;
        } else if (data.value && answer === false) {
          isdangerarr.value.fill(false);
        }
        return false;
      };

      const getData = async () => {
        const per = route.query.per || perpage.value;
        const a = route.query.a || false;
        const id = route.query.id || 48905;
        const z = route.query.z || 0;
        const b = route.query.b || 0;
        const page = route.query.page || current1.value;
        const type = route.query.type || 'gwy';
        // const f = new URLSearchParams(window.location.search).get('f');
        const url = '/egg/fbtimu';
        let params = {
          per: per,
          page: page,
          id: id,
          type: type,
          z: z,
          b: b,
        };
        console.log(params);
        try {
          const response = await axios.get(url, { params });
          if (response.data.length === 0) {
            console.log('z1', z);
            await getData();
            return;
          }
          if (a) {
            showContent.value = true;
          }
          // console.log(response.data.pagetotal[0].total);

          data.value = response.data.data;

          if (+z === 1) {
            zltimu.value = response.data.data[0];
            // console.log(zltimu.value);
            data.value = response.data.data[1];
          }
          let x = [];
          if (+b === 1) {
            for (let item in data.value) {
              console.log(data.value[item].answer);
              let ans = data.value[item].answer;
              x.push({
                index: item + 1,
                //+item + 1红色

                answer: `<span style="color: red">` + (+item + 1) + `</span>` + `.` + ans,
                solution: data.value[item].solution,
              });
            }
            data.value = x;
          }
          total.value = response.data.pagetotal[0].total || 0;
          ansblack();
          // isDanger(false);

          // showContent.value = false;
          isLoading.value = false;
        } catch (error) {
          console.error(error);
          isLoading.value = false;
        }
      };

      onMounted(() => {
        document.addEventListener('keydown', toggleContent);
        getData();
        window.addEventListener('keydown', handleKeyDown);
        updateFontSize();
        getResolution();
        window.addEventListener('resize', getResolution);
      });

      onBeforeUnmount(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keydown', toggleContent);
        window.removeEventListener('resize', this.getResolution);
      });

      return {
        data,
        zltimu,
        isLoading,
        current1,
        showContent,
        pagetotal,
        perpage,
        total,
        left,
        right,
        togglean,
        getData,
        isDanger,
        isdangerarr,
        pushans,
        fontStyle,
        increaseFontSize,
        toggleTop,
        showTop,
        toggleHiddenTag,
        isTagHidden,
        typex,
        toggleshowdaan,
        showdaan,
        getResolution,
        resolution,
      };
    },
  };
</script>

<style>
  body {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    font-family: 'SimSun', sans-serif;
  }
  * {
    margin: 0;
    padding: 0;
  }

  * {
    font-size: 18px;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  .items {
    border-left: 2px solid black;
    padding-left: 10px;
  }
  .top {
    background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
    position: fixed;
    bottom: 0;
  }
  img {
    max-width: 100%;
    max-height: 100%;
  }
  .top {
    display: flex;
    justify-content: space-between;
  }

  .top-button,
  .top-input {
    display: flex;
    gap: 10px; /* Adjust the gap as needed */
  }
  .wp {
    color: black;
    height: 100%;
    width: 100%; /* 宽度占满父容器 */
    max-width: 100vw; /* 最大宽度不超过视口宽度 */
    word-wrap: break-word; /* 在单词内部进行换行 */
    overflow-x: hidden;
    padding-top: 10px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }
  @media only screen and (max-width: 576px) {
    :where(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    :where(.css-dev-only-do-not-override-hkh161).ant-pagination {
      width: 100%;
    }
  }

  @media only screen and (max-width: 576px) {
    * {
      font-size: 20px;
    }
    .rb {
      background-color: rgba(240, 240, 240, 0.3);
    }
    :global(.ant-pagination .ant-pagination-options) {
      display: inline-block !important;
    }
    .top-button {
      display: none;
    }
    .top-input {
      display: none;
    }
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
