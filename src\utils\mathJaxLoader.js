/**
 * 🧮 MathJax动态加载器
 * 功能：按需动态加载MathJax CDN，支持单例模式和并发控制
 * 作者：优化方案 - 将MathJax从全局加载改为按需加载
 */

class MathJaxLoader {
  constructor() {
    this.isLoaded = false; // 是否已加载完成
    this.isLoading = false; // 是否正在加载
    this.loadPromise = null; // 加载Promise，用于并发控制
    this.cdnUrl = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg-full.js'; // CDN地址
    this.loadTimeout = 10000; // 加载超时时间（10秒）
  }

  /**
   * 🎯 动态加载MathJax（单例模式 + 并发控制）
   * @param {Object} customConfig - 自定义配置，可覆盖默认配置
   * @returns {Promise} 加载完成的Promise
   */
  async loadMathJax(customConfig = {}) {
    // 🚀 优化1：如果已经加载完成，直接返回
    if (this.isLoaded && window.MathJax) {
      console.log('🧮 MathJax已加载，直接使用');
      return Promise.resolve();
    }

    // 🚀 优化2：如果正在加载，返回现有的Promise
    if (this.isLoading && this.loadPromise) {
      console.log('🔄 MathJax正在加载，等待现有请求');
      return this.loadPromise;
    }

    // 🚀 优化3：开始新的加载流程
    console.log('📐 开始动态加载MathJax...');
    this.isLoading = true;
    
    this.loadPromise = this._performLoad(customConfig)
      .then(() => {
        this.isLoaded = true;
        this.isLoading = false;
        console.log('✅ MathJax动态加载完成');
        return Promise.resolve();
      })
      .catch(error => {
        this.isLoading = false;
        this.loadPromise = null;
        console.error('❌ MathJax加载失败:', error);
        throw error;
      });

    return this.loadPromise;
  }

  /**
   * 🔧 私有方法：执行实际的加载流程
   * @param {Object} customConfig - 自定义配置
   * @returns {Promise} 加载Promise
   */
  async _performLoad(customConfig) {
    // 第1步：设置MathJax配置
    this._setupConfig(customConfig);
    
    // 第2步：动态加载脚本
    await this._loadScript();
    
    // 第3步：等待MathJax初始化完成
    await this._waitForInitialization();
  }

  /**
   * 🔧 私有方法：设置MathJax配置（完整复制index.html中的配置）
   * @param {Object} customConfig - 自定义配置
   */
  _setupConfig(customConfig) {
    console.log('🔧 设置MathJax配置...');
    
    // 默认配置（完整复制index.html中第13-44行的配置）
    const defaultConfig = {
      tex: {
        inlineMath: [['$', '$'], ['\\(', '\\)']],
        displayMath: [['$$', '$$'], ['\\[', '\\]']],
        processEscapes: true,
        // 确保加载所有需要的包，特别是 ams (for boxed)、color 和 cancel
        packages: {'[+]': ['ams', 'color', 'cancel', 'boldsymbol', 'textmacros']},
        // 修复：确保单个 $$ 公式显示为内联模式
        tags: 'ams'
      },
      // 添加 CHTML 配置以确保正确的显示模式
      chtml: {
        displayAlign: 'left',
        displayIndent: '0em'
      },
      // 关键修复：配置MathJax处理所有元素，包括pre和code
      options: {
        skipHtmlTags: ['script', 'noscript', 'style', 'textarea'], // 移除了'pre'和'code'
        ignoreHtmlClass: 'tex2jax_ignore',
        processHtmlClass: 'tex2jax_process'
      },
      // 切换到 SVG 输出模式，以获得更好的兼容性和渲染效果
      svg: {
        fontCache: 'global'
      },
      startup: {
        ready: () => {
          console.log('🧮 MathJax is ready with SVG output! (支持pre/code块)');
          if (window.MathJax && window.MathJax.startup && window.MathJax.startup.defaultReady) {
            window.MathJax.startup.defaultReady();
          }
        }
      }
    };

    // 合并自定义配置
    const finalConfig = this._deepMerge(defaultConfig, customConfig);
    
    // 设置全局MathJax配置
    window.MathJax = finalConfig;
    
    console.log('✅ MathJax配置设置完成');
  }

  /**
   * 🔧 私有方法：动态创建script标签加载CDN
   * @returns {Promise} 脚本加载Promise
   */
  _loadScript() {
    return new Promise((resolve, reject) => {
      console.log('📡 开始加载MathJax CDN:', this.cdnUrl);
      
      // 检查是否已经存在MathJax脚本
      const existingScript = document.getElementById('MathJax-script');
      if (existingScript) {
        console.log('🔍 发现已存在的MathJax脚本，移除后重新加载');
        existingScript.remove();
      }

      // 创建新的script标签
      const script = document.createElement('script');
      script.id = 'MathJax-script';
      script.src = this.cdnUrl;
      script.async = true;

      // 设置加载超时
      const timeoutId = setTimeout(() => {
        script.remove();
        reject(new Error(`MathJax加载超时（${this.loadTimeout}ms）`));
      }, this.loadTimeout);

      // 脚本加载成功
      script.onload = () => {
        clearTimeout(timeoutId);
        console.log('✅ MathJax CDN加载成功');
        resolve();
      };

      // 脚本加载失败
      script.onerror = (error) => {
        clearTimeout(timeoutId);
        script.remove();
        console.error('❌ MathJax CDN加载失败:', error);
        reject(new Error('MathJax CDN加载失败，请检查网络连接'));
      };

      // 添加到页面
      document.head.appendChild(script);
    });
  }

  /**
   * 🔧 私有方法：等待MathJax初始化完成
   * @returns {Promise} 初始化完成Promise
   */
  _waitForInitialization() {
    return new Promise((resolve, reject) => {
      console.log('⏳ 等待MathJax初始化完成...');
      
      const checkInterval = 100; // 检查间隔100ms
      const maxWaitTime = 5000; // 最大等待时间5秒
      let waitedTime = 0;

      const checkReady = () => {
        if (window.MathJax && window.MathJax.startup && window.MathJax.typesetPromise) {
          console.log('✅ MathJax初始化完成');
          resolve();
          return;
        }

        waitedTime += checkInterval;
        if (waitedTime >= maxWaitTime) {
          reject(new Error('MathJax初始化超时'));
          return;
        }

        setTimeout(checkReady, checkInterval);
      };

      checkReady();
    });
  }

  /**
   * 🔧 私有方法：深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  _deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this._deepMerge(target[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  /**
   * 🧹 清理方法：重置加载状态（用于测试或重新加载）
   */
  reset() {
    console.log('🧹 重置MathJax加载器状态');
    this.isLoaded = false;
    this.isLoading = false;
    this.loadPromise = null;
    
    // 移除现有的MathJax脚本
    const existingScript = document.getElementById('MathJax-script');
    if (existingScript) {
      existingScript.remove();
    }
    
    // 清除全局MathJax对象
    if (window.MathJax) {
      delete window.MathJax;
    }
  }

  /**
   * 📊 获取加载器状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      isLoaded: this.isLoaded,
      isLoading: this.isLoading,
      hasPromise: !!this.loadPromise,
      hasMathJax: !!window.MathJax,
      cdnUrl: this.cdnUrl
    };
  }
}

// 🚀 创建全局单例
const mathJaxLoader = new MathJaxLoader();

export default mathJaxLoader;

/**
 * 🎯 使用示例：
 * 
 * import mathJaxLoader from '@/utils/mathJaxLoader'
 * 
 * // 在组件中使用
 * async function renderMath() {
 *   try {
 *     // 确保MathJax已加载
 *     await mathJaxLoader.loadMathJax();
 *     
 *     // 使用MathJax渲染数学公式
 *     await window.MathJax.typesetPromise([element]);
 *   } catch (error) {
 *     console.error('MathJax渲染失败:', error);
 *   }
 * }
 * 
 * // 自定义配置示例
 * await mathJaxLoader.loadMathJax({
 *   tex: {
 *     packages: {'[+]': ['ams', 'color']} // 只加载部分包
 *   }
 * });
 */
