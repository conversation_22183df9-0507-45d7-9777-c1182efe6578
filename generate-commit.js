import { execSync } from 'child_process';
import fs from 'fs';
import { OpenAI } from 'openai';

// 确保变更文件存在
try {
  execSync('git diff > changes.diff', { stdio: 'inherit' });
  execSync('git status > status.txt', { stdio: 'inherit' });
  console.log('✅ 已生成变更文件');
} catch (error) {
  console.error('❌ 生成变更文件失败:', error.message);
  process.exit(1);
}

const siliai = new OpenAI({
  baseURL: 'https://api.siliconflow.cn/v1',
  apiKey: 'sk-jvyedhzpmkjtqxzcoqyfjhrygncnmylywtumnppkqzqnscqj',
});

// 读取Git变更内容和状态
const changes = fs.readFileSync('changes.diff', 'utf-8');
const status = fs.readFileSync('status.txt', 'utf-8');

const prompt = `[紧急] 请在10秒内生成符合要求的commit信息：
变更内容：
${changes}

状态信息：
${status}

请输出格式：
[功能/修复] 简短描述
- 详细描述1
- 详细描述2`;

(async () => {
  try {
    const response1 = await siliai.chat.completions.create({
      model: 'deepseek-ai/DeepSeek-R1',
      messages: [{ role: 'user', content: prompt }],
      stream: true,
    });

    let reasoning_content = '';
    let content = '';
    let isFirstChunk = true;

    for await (const chunk of response1) {
      // 打印第一个chunk以检查结构
      if (isFirstChunk) {
        console.log('第一个响应chunk结构:', JSON.stringify(chunk, null, 2));
        isFirstChunk = false;
      }

      const delta = chunk.choices?.[0]?.delta;
      if (!delta) continue;

      // 尝试处理思考过程（根据实际字段名称调整）
      if (delta?.reasoning_content) {
        // 原始字段名
        process.stdout.write(delta.reasoning_content);
        reasoning_content += delta.reasoning_content;
      } else if (delta?.explanation) {
        // 可能的替代字段名
        process.stdout.write(delta.explanation);
        reasoning_content += delta.explanation;
      } else if (delta?.content) {
        // 处理主要内容
        process.stdout.write(delta.content);
        content += delta.content;
      }
    }
    process.stdout.write('\n');

    // 提取提交信息
    const firstLine = content.split('\n').find((line) => line.trim() !== '') || '[未指定]';
    const regex = /\[([^\]]+)\](.*)/;
    const match = firstLine.match(regex);
    const commitTitle = match ? `[${match[1]}]${match[2].trim()}` : firstLine;

    console.log('提取的提交标题:', commitTitle);

    // 保存完整提交信息到文件
    fs.writeFileSync('commit_msg.txt', content);

    // 执行提交
    try {
      execSync('git add .', { stdio: 'inherit' });
      execSync(`git commit -m "${commitTitle}"`, { stdio: 'inherit' });
      execSync('git push', { stdio: 'inherit' });
      console.log('✅ 代码已提交并推送');
    } catch (error) {
      console.error('❌ 提交或推送失败:', error.message);
      throw error;
    } finally {
      fs.unlinkSync('commit_msg.txt');
      // 新增：无论成功或失败都删除changes.diff和status.txt
      try {
        fs.unlinkSync('changes.diff');
      } catch (e) {}
      try {
        fs.unlinkSync('status.txt');
      } catch (e) {}
    }
  } catch (error) {
    console.error('处理过程中出错:', error);
    // 新增：异常时也删除changes.diff和status.txt
    try {
      fs.unlinkSync('changes.diff');
    } catch (e) {}
    try {
      fs.unlinkSync('status.txt');
    } catch (e) {}
    process.exit(1);
  }
})();
