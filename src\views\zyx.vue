<script>
  export default {
    name: 'Zyx',
    data: function () {
      return {
        tableData: [],
      };
    },
    mounted() {
      this.getData();
    },
    methods: {
      getData() {
        axios.get('/egg/zyx').then((res) => {
          this.tableData = res.data.data.list;
        });
      },
      indexMethod(index) {
        return index + 1;
      },
      tableRowClassName({ row, rowIndex }) {
        if (row.job_id === 1565) {
          return 'backgroundColor';
        } else if (row.job_id === 1565) {
          return 'success-row';
        }
        return '';
      },
    },
  };
</script>

<template>
  <div>
    <el-table
      :data="tableData"
      style="width: 100%"
      height="100%"
      border
      :row-class-name="tableRowClassName"
      :default-sort="{ prop: 'index', order: 'descending' }"
    >
      <el-table-column label="序号" type="index" :index="indexMethod"> </el-table-column>

      <el-table-column prop="job_id" label="ID" sortable> </el-table-column>
      <el-table-column prop="unit_name" label="公司" sortable> </el-table-column>
      <el-table-column prop="job_title" label="职位" sortable> </el-table-column>
      <el-table-column prop="total_count" label="总人数" sortable> </el-table-column>

      <el-table-column prop="no_check_count" label="待审核人数" sortable> </el-table-column>
      <el-table-column prop="pass_count" label="	审核通过人数" sortable> </el-table-column>
      <el-table-column prop="no_pass_count" label="审核不通过人数" sortable> </el-table-column>
    </el-table>
  </div>
</template>

<style scoped>
  .el-table__row .my-warning-row {
    background: #ffff90;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .warning-row {
    background: #ffff90;
  }
</style>
<style src="../../public/zyx.css"></style>
