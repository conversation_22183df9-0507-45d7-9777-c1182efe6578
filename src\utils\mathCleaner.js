/**
 * 数学公式清理工具
 * 专门处理复杂的LaTeX语法，使其在Web环境下正常显示
 */

/**
 * 清理TikZ语法，替换为Web兼容的数学符号
 * @param {string} content - 包含数学公式的内容
 * @returns {string} - 清理后的内容
 */
export function cleanTikZSyntax(content) {
  let cleaned = content;
  
  // 1. 替换复杂的TikZ交叉符号为简单的数学符号
  cleaned = cleaned.replace(
    /\\mathbin\{\\tikz\[x=1\.4ex,y=1\.4ex,line width=\.2ex, red\] \\draw \(0,0\) -- \(1,1\) \(0,1\) -- \(1,0\);\}/g,
    '\\textcolor{red}{\\times}'
  );
  
  // 2. 通用TikZ mathbin清理
  cleaned = cleaned.replace(
    /\\mathbin\{\\tikz\[.*?\].*?\}/g,
    '\\textcolor{red}{\\times}'
  );
  
  // 3. 清理所有TikZ绘图命令
  cleaned = cleaned.replace(
    /\\tikz\[.*?\]\\draw.*?;/g,
    '\\textcolor{red}{\\times}'
  );
  
  // 4. 清理残留的draw命令
  cleaned = cleaned.replace(
    /\\draw\s*\([^)]*\)\s*--\s*\([^)]*\)/g,
    ''
  );
  
  return cleaned;
}

/**
 * 清理复杂的LaTeX语法，使其更适合Web显示
 * @param {string} content - 原始内容
 * @returns {string} - 清理后的内容
 */
export function cleanComplexLatex(content) {
  let cleaned = content;
  
  // 清理TikZ语法
  cleaned = cleanTikZSyntax(cleaned);
  
  // 其他复杂语法的清理可以在这里添加
  
  return cleaned;
}

/**
 * 为您的具体内容提供优化版本
 * @param {string} originalContent - 原始内容
 * @returns {string} - 优化后的内容
 */
export function optimizeForWeb(originalContent) {
  let optimized = originalContent;
  
  // 替换复杂的交叉关系公式为简化版本
  const complexFormula = /\$\$\s*\\begin\{align\*\}[\s\S]*?\\end\{align\*\}\s*\$\$/g;
  
  optimized = optimized.replace(complexFormula, (match) => {
    if (match.includes('tikz')) {
      return `
**交叉关系判定式**：

$$
\\begin{align*}
\\text{题干} &: \\overbrace{\\text{接力}}^{\\text{形式}} \\textcolor{red}{\\times} \\overbrace{\\text{跑步}}^{\\text{内容}} \\\\
\\text{D项} &: \\underbrace{\\text{木质}}_{\\text{材质}} \\textcolor{red}{\\times} \\underbrace{\\text{办公}}_{\\text{功能}} 
\\end{align*}
$$
`;
    }
    return match;
  });
  
  return optimized;
}

/**
 * 提供多种显示选项
 */
export const displayOptions = {
  // 选项1：使用红色乘号
  crossSymbol1: '\\textcolor{red}{\\times}',
  
  // 选项2：使用交叉符号
  crossSymbol2: '\\textcolor{red}{\\otimes}',
  
  // 选项3：使用简单文字
  crossSymbol3: '\\text{ 交叉 }',
  
  // 选项4：使用箭头
  crossSymbol4: '\\textcolor{red}{\\leftrightarrow}',
};

/**
 * 生成简化版本的内容
 * @param {string} originalContent - 原始内容
 * @returns {string} - 简化版本
 */
export function generateSimplifiedVersion(originalContent) {
  return `
### 📜 **草稿纸极简秒题手稿（1句破题！）**

🔥 **题目浓缩**：**找两组词"你中有我，我中有你"的关系！**  
**破绽**：**只有D项才是"材质vs功能"双视角交叉→其他项全是单视角硬凑！**

| 选项 | 关系类型 | 致命漏洞 💣 | 表情包判官⚖️ |
|------|----------|------------|--------------|
| **D.木制桌椅→办公桌椅** | ✅ **交叉关系**（材质🆚功能） | ✅ **完美克隆题干DNA**（接力=形式，跑步=内容） | "显微镜：确认是亲兄弟！🔬" |
| A.行政人员→科研人员 | ❌ **并列关系**（不同职业） | ❗️ **强行认亲**（题干是交叉，这里是平行线） | "职业中介：别乱攀亲戚！👔" |
| B.艺术展览→美术展览 | ❌ **种属关系**（美术⊂艺术） | ❗️ **父子冒充兄弟**（题干是平等交叉） | "艺术馆：我是你爹！🎨" |
| C.智能手机→5G手机 | ❌ **种属关系**（5G⊂智能） | ❗️ **祖孙三代乱入**（层级碾压） | "手机店：5G是智能的崽！📱" |

**逻辑核爆链💥（6行定生死）**：

1. **题干解剖**：  
   "接力比赛🆚跑步比赛"  
   → 接力是**比赛形式**，跑步是**比赛内容**  
   → **形式≠内容** → 存在交叉 ✅

2. **交叉关系公式**：  
   **题干**: 接力(形式) ⊗ 跑步(内容)  
   **D项**: 木质(材质) ⊗ 办公(功能)

3. **选项验尸**：  
   ► D ✅：木质（材质）∩办公（功能）≠∅ （如：木制办公椅）  
   ► A ❌：行政∩科研=∅ （水火不容）  
   ► B/C ❌：艺术⊃美术，智能⊃5G（单边包含）

**结论**：仅D满足 $\\textcolor{green}{\\text{A} \\not\\subseteq \\text{B} \\quad \\& \\quad \\text{B} \\not\\subseteq \\text{A}}$

---

### ✍️ **考场做题实录（8秒撕卷⏱️）**

1. **闪电定位题干基因⚡（⏱️1秒）**：  
   > 抓关键词 **"接力"和"跑步"** →  
   > 接力是**玩法**（如游泳接力/火炬接力），跑步是**运动类型** →  
   > **本质：形式vs内容的交叉** ✅

2. **选项血洗计划💣（⏱️5秒）**：  
   ► **D.右组=办公桌椅** → ✅ 立即锁定！  
   ► A.行政vs科研 → ❌ 平行世界关系  
   ► B.艺术vs美术 → ❌ 爸爸和儿子关系  
   ► C.智能vs5G → ❌ 爷爷和孙子关系

3. **数学封印公式🔢（⏱️2秒）**：  
   > **交叉关系判定**：形式 ⊗ 内容 = 材质 ⊗ 功能 ✅

**你是最棒的哟！** 🌟 视角的刀锋切开词语的迷雾！下一题继续碾压！
`;
}
