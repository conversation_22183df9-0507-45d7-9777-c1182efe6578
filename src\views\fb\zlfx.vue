<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-show="showTop" class="top">
          <a-space>
            <!--            <a-button @click="increaseFontSize">插到数据库</a-button>-->
            <a-button class="showans" @click="togglean">显示答案</a-button>
            <a-button>{{ total }}</a-button>
            <a-button @click="toggleTop">隐藏顶部内容</a-button>
            <fbhiddentag></fbhiddentag>
          </a-space>
          <a-space>
            <a-select
              ref="select"
              v-model:value="value1"
              style="width: 120px"
              @focus="focus"
              @change="handleChange"
            >
              <a-select-option value="48644">gwy</a-select-option>
              <a-select-option value="656604">sy</a-select-option>
            </a-select>
            <a-input v-model:value="timu" placeholder="timu" @change="getData(timu)" />
            <a-input v-model:value="biaoming" placeholder="表名"></a-input>
            <a-input v-model:value="ids" placeholder="ids"></a-input>
            <a-button type="primary" @click="getData">搜索</a-button>
            <a-button type="primary" @click="increaseFontSize">复制</a-button>
          </a-space>
        </div>
        <div v-html="zltimu.content"></div>
        <span>{{ zltimu.id }}</span>
        <div v-for="(item, index) in data" :key="index">
          <div v-if="isLoading">Loading...</div>
          <div v-if="!isLoading">
            <div v-html="item.content"></div>
            {{ item.source }}
            <!--            <fbtag-->
            <!--              :messageFromParent="item.id"-->
            <!--              :messageFromParent1="value1 === 48644 ? 'gwy' : 'sy'"-->
            <!--            ></fbtag>-->
            <div class="item" @click="togglean">
              <p class="an_a" v-html="item.answerone"></p>
              <p class="an_b" v-html="item.answertwo"></p>
              <p class="an_c" v-html="item.answerthree"></p>
              <p class="an_d" v-html="item.answerfour"></p>
            </div>
            <p @click="toggleTop">====================================</p>
            <div v-show="showContent" class="answer">
              <!--              <div v-html="item.answer"></div>-->
              <div>{{ item.source }}{{ item.id }}</div>
              <br />
              <div v-html="item.solution"></div>
              <div>
                <dp :url="item.video" :uid="item.id"></dp>
              </div>
              <p>====================================</p>
            </div>
          </div>
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';
  import fbtag from '../../components/fbtag.vue';
  import fbhiddentag from '../../components/fbtogglehiddentag.vue';
  import { message } from 'ant-design-vue';

  const zltimu = ref([]);
  const data = ref([]);
  const isLoading = ref(true);
  const showTop = ref(true);
  const current1 = ref(1);
  const showContent = ref(false);
  const pagetotal = ref(20);
  const pageSize3 = ref(1);
  const total = ref(0);
  const isdangerarr = ref([false, false, false, false]);
  const route = useRoute();
  const fontStyle = ref(16);
  const value1 = ref(48644);
  const timu = ref('');
  const timuid = ref(0);
  const biaoming = ref('');
  const ids = ref('');
  const toggleTop = () => {
    showTop.value = !showTop.value;
  };
  const increaseFontSize = async () => {
    const url = '/egg/fbupdatezlfx5000';
    let params = {
      id: timuid.value,
      biao: biaoming.value,
    };
    console.log(params);

    try {
      const response = await axios.get(url, { params });
      if (response.status === 200) {
        message.success('创建成功');
      }
    } catch (error) {
      message.error(error.response.data.message);
    }
  };
  const updateFontSize = () => {
    const wpElement = document.querySelector('.wp');
    wpElement.style.fontSize = `${fontStyle.value}px`;
  };
  const handleKeyDown = (event) => {
    if (event.key === 'ArrowLeft' || event.key === 'q') {
      getData();
      ansblack();
    } else if (event.key === 'ArrowRight' || event.key === 'e') {
      increaseFontSize();
    }
  };

  const right = () => {
    if (current1.value < 10000) {
      current1.value += 1;
      getData();
      ansblack();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };

  const left = () => {
    if (current1.value > 1) {
      current1.value -= 1;
      getData();
      ansblack();
      if (showContent.value) {
        ansblack();
        isDanger(false);

        showContent.value = false;
      }
    }
  };
  const toggleContent = (event) => {
    if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      if (showContent.value) {
        ansblack();
        isDanger(false);
        showContent.value = false;
      } else {
        ansred();
        const showAnsButton = document.querySelector('.showans');
        showAnsButton.click();
        showContent.value = true;
      }
    }
  };

  const ansred = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'red';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'red';
      }
    }
  };

  const ansblack = () => {
    for (let i = 0; i < data.value.length; i++) {
      if (data.value && data.value[i].answer === 'A') {
        document.getElementsByClassName('an_a')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'B') {
        document.getElementsByClassName('an_b')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'C') {
        document.getElementsByClassName('an_c')[i].style.color = 'black';
      }
      if (data.value && data.value[i].answer === 'D') {
        document.getElementsByClassName('an_d')[i].style.color = 'black';
      }
    }
  };

  const togglean = (answer) => {
    if (showContent.value) {
      ansblack();
      isDanger(false);
      showContent.value = false;
    } else {
      ansred();
      isDanger(answer);
      showContent.value = true;
    }
  };

  const pushans = async (answer) => {
    // const url = '/egg/minchoice';
    // data.value.choice = answer;
    // const response = await axios.post(url, data.value);
    // console.log(response);
  };

  const isDanger = (answer) => {
    if (data.value && data.value.answer === answer && answer !== false) {
      switch (answer) {
        case 'A':
          isdangerarr.value[0] = true;
          break;
        case 'B':
          isdangerarr.value[1] = true;
          break;
        case 'C':
          isdangerarr.value[2] = true;
          break;
        case 'D':
          isdangerarr.value[3] = true;
          break;
      }
      return true;
    } else if (data.value && answer === false) {
      isdangerarr.value.fill(false);
    }
    return false;
  };
  const focus = () => {
    console.log('focus');
  };
  const handleChange = (value) => {
    console.log(`selected ${value}`);
  };
  const getData = async () => {
    const per = route.query.per || pageSize3.value;
    const a = route.query.a || false;
    const id = route.query.id || 48905;
    const z = route.query.z || 0;
    const page = route.query.page || current1.value;
    const type = route.query.type || 'gwy';
    // const f = new URLSearchParams(window.location.search).get('f');
    const url = '/egg/fbchazlfx';
    // const clipboardData = await navigator.clipboard.readText();
    // console.log('剪贴板', clipboardData);
    // timu.value = clipboardData;
    let params = {
      typeid: value1.value,
      timu: timu.value,
    };
    console.log(params);
    try {
      const response = await axios.get(url, { params });
      if (response.data.length === 0) {
        console.log('z1', z);
        await getData();
        return;
      }
      if (a) {
        showContent.value = true;
      }
      // console.log(response.data.pagetotal[0].total);

      data.value = response.data.data;
      zltimu.value = response.data.data[0];
      console.log(zltimu.value);
      data.value = response.data.data[1];
      console.log(data.value);
      let k = data.value.map((item) => item.id);
      console.log(k);
      //把k转换成字符串
      ids.value = k.join();
      // ids.value =;
      // total.value = response.data.pagetotal[0].total || 0;
      timuid.value = data.value[0].parentid ? data.value[0].parentid : data.value[0].id;
      showContent.value = false;
      isLoading.value = false;
    } catch (error) {
      console.error(error);
      isLoading.value = false;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', toggleContent);
    getData();
    window.addEventListener('keydown', handleKeyDown);
    updateFontSize();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keydown', toggleContent);
  });
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
