import { test, expect } from '@playwright/test';

test.describe('Math Formula Rendering Analysis', () => {
  const testUrl = '/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123';
  
  test.beforeEach(async ({ page }) => {
    // Enable console logging to capture MathJax logs
    page.on('console', msg => {
      console.log(`[${msg.type()}] ${msg.text()}`);
    });
    
    // Capture network errors
    page.on('requestfailed', request => {
      console.log(`❌ Failed request: ${request.url()} - ${request.failure()?.errorText}`);
    });
  });

  test('should load page and analyze MathJax setup', async ({ page }) => {
    console.log('🚀 Starting MathJax analysis test...');
    
    // Navigate to the test URL
    await page.goto(testUrl);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check if MathJax is loaded
    const mathJaxLoaded = await page.evaluate(() => {
      return typeof window.MathJax !== 'undefined';
    });
    
    console.log(`📐 MathJax loaded: ${mathJaxLoaded}`);
    expect(mathJaxLoaded).toBe(true);
    
    // Check MathJax configuration
    const mathJaxConfig = await page.evaluate(() => {
      if (window.MathJax) {
        return {
          version: window.MathJax.version,
          config: window.MathJax.config,
          startup: !!window.MathJax.startup,
          typesetPromise: typeof window.MathJax.typesetPromise === 'function'
        };
      }
      return null;
    });
    
    console.log('🔧 MathJax Configuration:', JSON.stringify(mathJaxConfig, null, 2));
    expect(mathJaxConfig).not.toBeNull();
  });

  test('should detect shuxue.vue component and math content', async ({ page }) => {
    console.log('🔍 Analyzing shuxue.vue component...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Look for shuxue component container
    const shuxueContainer = page.locator('.markdown-content.prose.max-w-none.tex2jax_process');
    await expect(shuxueContainer).toBeVisible({ timeout: 10000 });
    
    // Check for math formulas in the content
    const mathFormulas = await page.evaluate(() => {
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!container) return { found: false, details: 'Container not found' };
      
      const content = container.innerHTML;
      const patterns = {
        inlineMath: (content.match(/\$[^$]+\$/g) || []).length,
        displayMath: (content.match(/\$\$[\s\S]+?\$\$/g) || []).length,
        latexBrackets: (content.match(/\\\[[\s\S]+?\\\]/g) || []).length,
        latexParens: (content.match(/\\\([\s\S]+?\\\)/g) || []).length,
        mjxContainers: container.querySelectorAll('mjx-container').length,
        mathElements: container.querySelectorAll('[class*="math"], .tex2jax_process').length
      };
      
      return {
        found: true,
        patterns,
        contentLength: content.length,
        hasContent: content.length > 0,
        sampleContent: content.substring(0, 500) + '...'
      };
    });
    
    console.log('📊 Math Formula Analysis:', JSON.stringify(mathFormulas, null, 2));
    expect(mathFormulas.found).toBe(true);
  });

  test('should analyze MathJax rendering process', async ({ page }) => {
    console.log('⚙️ Analyzing MathJax rendering process...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Wait for potential MathJax rendering
    await page.waitForTimeout(3000);
    
    // Check MathJax rendering status
    const renderingStatus = await page.evaluate(async () => {
      if (!window.MathJax) return { error: 'MathJax not loaded' };
      
      try {
        // Check if MathJax has finished startup
        if (window.MathJax.startup?.promise) {
          await window.MathJax.startup.promise;
        }
        
        const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
        if (!container) return { error: 'Math container not found' };
        
        // Force a re-render to test the process
        if (window.MathJax.typesetPromise) {
          await window.MathJax.typesetPromise([container]);
        }
        
        return {
          success: true,
          renderedElements: container.querySelectorAll('mjx-container').length,
          mathElements: container.querySelectorAll('[class*="math"]').length,
          errors: window.MathJax.startup?.document?.math?.errors || [],
          containerClasses: container.className
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('🎯 MathJax Rendering Status:', JSON.stringify(renderingStatus, null, 2));
    
    if (renderingStatus.error) {
      console.error('❌ MathJax rendering error:', renderingStatus.error);
    } else {
      expect(renderingStatus.success).toBe(true);
      console.log(`✅ Successfully rendered ${renderingStatus.renderedElements} math elements`);
    }
  });

  test('should capture screenshots for visual analysis', async ({ page }) => {
    console.log('📸 Capturing screenshots for visual analysis...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    
    // Wait for MathJax rendering
    await page.waitForTimeout(5000);
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/math-page-full.png', 
      fullPage: true 
    });
    
    // Take screenshot of math content area if it exists
    const mathContainer = page.locator('.markdown-content.prose.max-w-none.tex2jax_process');
    if (await mathContainer.isVisible()) {
      await mathContainer.screenshot({ 
        path: 'test-results/math-content-area.png' 
      });
    }
    
    console.log('📸 Screenshots saved to test-results/');
  });

  test('should analyze potential rendering issues', async ({ page }) => {
    console.log('🔍 Analyzing potential rendering issues...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    const issues = await page.evaluate(() => {
      const issues = [];
      const container = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      
      if (!container) {
        issues.push('Math container not found');
        return issues;
      }
      
      // Check for unrendered math
      const unrenderedMath = container.innerHTML.match(/\$[^$]+\$|\$\$[\s\S]+?\$\$|\\\[[\s\S]+?\\\]/g);
      if (unrenderedMath && unrenderedMath.length > 0) {
        issues.push(`Found ${unrenderedMath.length} potentially unrendered math expressions`);
      }
      
      // Check for MathJax errors
      const errorElements = container.querySelectorAll('.mjx-error, [data-mjx-error]');
      if (errorElements.length > 0) {
        issues.push(`Found ${errorElements.length} MathJax error elements`);
      }
      
      // Check for missing mjx-container elements
      const mjxContainers = container.querySelectorAll('mjx-container');
      const mathPatterns = (container.innerHTML.match(/\$|\\\[|\\\(/g) || []).length;
      if (mathPatterns > 0 && mjxContainers.length === 0) {
        issues.push('Math patterns found but no rendered MathJax containers');
      }
      
      // Check for console errors (this would need to be captured differently)
      if (window.console && window.console.errors) {
        issues.push(`Console errors: ${window.console.errors.length}`);
      }
      
      return issues;
    });
    
    console.log('🚨 Potential Issues Found:', issues);
    
    if (issues.length > 0) {
      console.warn('⚠️ Math rendering issues detected:');
      issues.forEach(issue => console.warn(`  - ${issue}`));
    } else {
      console.log('✅ No obvious rendering issues detected');
    }
    
    // This test doesn't fail on issues, just reports them
    expect(Array.isArray(issues)).toBe(true);
  });
});
