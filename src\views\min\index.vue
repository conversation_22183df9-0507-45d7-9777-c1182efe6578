<template>
  <a-row type="flex">
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="lb" @click="left"></div>
    </a-col>
    <a-col :lg="14" :sm="22" :xs="22" :md="22">
      <div class="wp">
        <div v-if="isLoading">Loading...</div>
        <div v-if="!isLoading">
          <a-button @click="right">下一页</a-button>
          <a v-for="item in list" :key="item" :href="'/min?f=' + item" target="_blank">
            <a-button size="small">{{ item }}</a-button>
          </a>
          <a-button @click="increaseFontSize">增大字体</a-button>
          <div>
            <div v-if="data && data.materialContent" v-html="data.materialContent"></div>
          </div>
          <div v-html="data && data.content"></div>
          <div class="item">
            <p class="an_a" @click="pushans('A')">
              <a-button :danger="isdangerarr[0]" v-html="data && data.A"></a-button>
            </p>
            <p class="an_b" @click="pushans('B')">
              <a-button :danger="isdangerarr[1]" v-html="data && data.B"></a-button>
            </p>
            <p class="an_c" @click="pushans('C')">
              <a-button :danger="isdangerarr[2]" v-html="data && data.C"></a-button>
            </p>
            <p class="an_d" @click="pushans('D')">
              <a-button :danger="isdangerarr[3]" v-html="data && data.D"></a-button>
            </p>
            <p class="showans" @click="togglean(data.referenceAnswer)">
              <a-button type="primary">显示答案 {{ data.id }} {{ data.finish }}</a-button>
            </p>
          </div>
          <div v-show="showContent" class="answer">
            <p style="color: dodgerblue" v-html="data && data.choice"></p>
            <div v-html="data && data.referenceAnswer"></div>
            <div v-html="data && data.referenceAnalysis"></div>
          </div>
          <a-pagination
            v-model:current="current1"
            show-quick-jumper
            :total="pagetotal"
            PageSize="1"
            @change="getData"
          />
        </div>
      </div>
    </a-col>
    <a-col :lg="5" :sm="1" :xs="1" :md="1">
      <div class="rb" @click="right"></div>
    </a-col>
  </a-row>
</template>

<script>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import axios from 'axios';
  import { useRoute } from 'vue-router';

  export default {
    name: 'Min',
    setup() {
      const data = ref([]);
      const isLoading = ref(true);
      const current1 = ref(1);
      const showContent = ref(false);
      const pagetotal = ref(20);
      const list = ref(['政治', '经济', '法律', '管理', '片段阅读']);
      const isdangerarr = ref([false, false, false, false]);
      const route = useRoute();
      const fontStyle = ref(16);

      const increaseFontSize = () => {
        fontStyle.value += 2;
        updateFontSize();
      };
      const updateFontSize = () => {
        const wpElement = document.querySelector('.wp');
        wpElement.style.fontSize = `${fontStyle.value}px`;
      };
      const handleKeyDown = (event) => {
        if (event.key === 'ArrowLeft' || event.key === 'q') {
          if (current1.value > 1) {
            current1.value -= 1;
            getData();
            if (showContent.value) {
              // ansblack();
              isDanger(false);

              showContent.value = false;
            }
          }
        } else if (event.key === 'ArrowRight' || event.key === 'e') {
          if (current1.value < 10000) {
            current1.value += 1;
            getData();
            if (showContent.value) {
              // ansblack();
              isDanger(false);

              showContent.value = false;
            }
          }
        }
      };

      const right = () => {
        if (current1.value < 10000) {
          current1.value += 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };

      const left = () => {
        if (current1.value > 1) {
          current1.value -= 1;
          getData();
          if (showContent.value) {
            ansblack();
            isDanger(false);

            showContent.value = false;
          }
        }
      };
      const toggleContent = (event) => {
        if (event.key === 'w' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
          if (showContent.value) {
            // ansblack();
            isDanger(false);
            showContent.value = false;
          } else {
            // ansred();
            const showAnsButton = document.querySelector('.showans');
            showAnsButton.click();
            showContent.value = true;
          }
        }
      };

      const ansred = () => {
        if (data.value && data.value.referenceAnswer === 'A') {
          document.getElementsByClassName('an_a')[0].style.color = 'red';
        }
        if (data.value && data.value.referenceAnswer === 'B') {
          document.getElementsByClassName('an_b')[0].style.color = 'red';
        }
        if (data.value && data.value.referenceAnswer === 'C') {
          document.getElementsByClassName('an_c')[0].style.color = 'red';
        }
        if (data.value && data.value.referenceAnswer === 'D') {
          document.getElementsByClassName('an_d')[0].style.color = 'red';
        }
      };

      const ansblack = () => {
        if (data.value && data.value.referenceAnswer === 'A') {
          document.getElementsByClassName('an_a')[0].style.color = 'black';
        }
        if (data.value && data.value.referenceAnswer === 'B') {
          document.getElementsByClassName('an_b')[0].style.color = 'black';
        }
        if (data.value && data.value.referenceAnswer === 'C') {
          document.getElementsByClassName('an_c')[0].style.color = 'black';
        }
        if (data.value && data.value.referenceAnswer === 'D') {
          document.getElementsByClassName('an_d')[0].style.color = 'black';
        }
      };

      const togglean = (answer) => {
        if (showContent.value) {
          // ansblack();
          isDanger(false);
          showContent.value = false;
        } else {
          // ansred();
          isDanger(answer);
          showContent.value = true;
        }
      };

      const pushans = async (answer) => {
        const url = '/egg/minchoice';
        data.value.choice = answer;
        const response = await axios.post(url, data.value);
        console.log(response);
      };

      const isDanger = (answer) => {
        if (data.value && data.value.referenceAnswer === answer && answer !== false) {
          switch (answer) {
            case 'A':
              isdangerarr.value[0] = true;
              break;
            case 'B':
              isdangerarr.value[1] = true;
              break;
            case 'C':
              isdangerarr.value[2] = true;
              break;
            case 'D':
              isdangerarr.value[3] = true;
              break;
          }
          return true;
        } else if (data.value && answer === false) {
          isdangerarr.value.fill(false);
        }
        return false;
      };

      const getData = async () => {
        const f = route.query.f;
        const s = route.query.s;
        // const f = new URLSearchParams(window.location.search).get('f');
        const url = '/egg/mintimu';
        let params = {
          per: 1,
          page: current1.value,
          j: 1,
        };

        if (f) {
          params = {
            f: f,
            per: 1,
            page: current1.value,
            j: 1,
          };
        }
        if (s) {
          params = {
            s: s,
            per: 1,
            page: current1.value,
            j: 1,
          };
        }

        try {
          const response = await axios.get(url, { params });
          if (response.length === 0) {
            await getData();
            return;
          }

          data.value = response.data[0];
          // if (data.value && data.value.referenceAnalysis) {
          //   data.value.referenceAnalysis = data.value.referenceAnalysis.replace(/<br>/g, '').replace(/选项/g, '<br><br>选项')
          // }
          if (data.value && data.value.pagetotal) {
            pagetotal.value = data.value.pagetotal * 10;
          }
          // ansblack();
          isDanger(false);

          showContent.value = false;
          isLoading.value = false;
        } catch (error) {
          console.error(error);
          isLoading.value = false;
        }
      };

      onMounted(() => {
        document.addEventListener('keydown', toggleContent);
        getData();
        window.addEventListener('keydown', handleKeyDown);
        updateFontSize();
      });

      onBeforeUnmount(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keydown', toggleContent);
      });

      return {
        data,
        isLoading,
        current1,
        showContent,
        pagetotal,
        list,
        left,
        right,
        togglean,
        getData,
        isDanger,
        isdangerarr,
        pushans,
        fontStyle,
        increaseFontSize,
      };
    },
  };
</script>

<style scoped>
  .wp {
    color: black;
    height: 100%;
    max-width: 960px;
  }

  .lb {
    height: 100%;
    z-index: 1;
  }

  .rb {
    height: 100%;
    z-index: 1;
  }

  @media screen and (max-width: 600px) {
    .wp {
      width: 100%;
    }
  }

  .answer {
    color: red;
  }

  @media only screen and (max-width: 576px) {
    :global(.ant-pagination-options) {
      display: inline-block !important;
    }
  }
</style>
