import { test, expect } from '@playwright/test';

test.describe('Math Formula Fix and Analysis', () => {
  const testUrl = '/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123';
  
  test.beforeEach(async ({ page }) => {
    // Enable console logging to capture MathJax logs
    page.on('console', msg => {
      console.log(`[${msg.type()}] ${msg.text()}`);
    });
    
    // Capture network errors
    page.on('requestfailed', request => {
      console.log(`❌ Failed request: ${request.url()} - ${request.failure()?.errorText}`);
    });
  });

  test('should fix unrendered math formulas and force re-render', async ({ page }) => {
    console.log('🚀 Starting math formula fix test...');
    
    // Navigate to the test URL
    await page.goto(testUrl);
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for initial MathJax rendering
    await page.waitForTimeout(3000);
    
    // Check initial state
    const initialState = await page.evaluate(() => {
      const mathContainer = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!mathContainer) return { error: 'Math container not found' };
      
      const content = mathContainer.innerHTML;
      return {
        unrenderedInlineMath: (content.match(/\$[^$]+\$/g) || []).length,
        renderedContainers: mathContainer.querySelectorAll('mjx-container').length,
        contentLength: content.length
      };
    });
    
    console.log('📊 Initial state:', initialState);
    
    // Fix broken math formulas
    const fixResult = await page.evaluate(() => {
      const mathContainer = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!mathContainer) return { error: 'Math container not found' };
      
      let content = mathContainer.innerHTML;
      let fixCount = 0;
      
      // Fix 1: Repair broken inline math that spans across HTML tags
      // Pattern: $</p><ol><li>content</li></ol> -> $$content$$
      const brokenMathPattern1 = /\$<\/p>\s*<ol>\s*<li>([^<]*(?:<[^>]*>[^<]*)*?)<\/li>\s*<\/ol>/g;
      content = content.replace(brokenMathPattern1, (match, mathContent) => {
        fixCount++;
        console.log(`🔧 Fix ${fixCount}: Repairing broken math across HTML tags`);
        // Clean up the math content and wrap it properly
        const cleanMath = mathContent.replace(/<[^>]*>/g, ' ').trim();
        return `$$${cleanMath}$$`;
      });
      
      // Fix 2: Repair inline math that got split by HTML
      // Pattern: $content$ that contains HTML tags
      const brokenMathPattern2 = /\$([^$]*<[^>]*>[^$]*)\$/g;
      content = content.replace(brokenMathPattern2, (match, mathContent) => {
        fixCount++;
        console.log(`🔧 Fix ${fixCount}: Cleaning HTML from inline math`);
        // Remove HTML tags from math content
        const cleanMath = mathContent.replace(/<[^>]*>/g, ' ').trim();
        return `$${cleanMath}$`;
      });
      
      // Fix 3: Find and fix math formulas that are not properly delimited
      // Look for LaTeX commands outside of math delimiters
      const latexCommandPattern = /(?<!\$)\\(?:text|color|xrightarrow|quad|equiv|checkmark|times)\{[^}]*\}(?!\$)/g;
      content = content.replace(latexCommandPattern, (match) => {
        fixCount++;
        console.log(`🔧 Fix ${fixCount}: Wrapping orphaned LaTeX command: ${match.substring(0, 20)}...`);
        return `$${match}$`;
      });
      
      // Fix 4: Repair display math that got broken
      const brokenDisplayMath = /\$\$([^$]*(?:\n[^$]*)*?)\$\$/g;
      content = content.replace(brokenDisplayMath, (match, mathContent) => {
        if (mathContent.includes('<') || mathContent.includes('>')) {
          fixCount++;
          console.log(`🔧 Fix ${fixCount}: Cleaning display math`);
          const cleanMath = mathContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
          return `$$${cleanMath}$$`;
        }
        return match;
      });
      
      // Apply the fixes
      if (fixCount > 0) {
        mathContainer.innerHTML = content;
        console.log(`✅ Applied ${fixCount} fixes to math formulas`);
      }
      
      return {
        fixCount,
        newUnrenderedInlineMath: (content.match(/\$[^$]+\$/g) || []).length,
        newContentLength: content.length
      };
    });
    
    console.log('🔧 Fix result:', fixResult);
    
    // Force MathJax to re-render the entire container
    await page.evaluate(async () => {
      const mathContainer = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!mathContainer || !window.MathJax) return;
      
      try {
        console.log('🔄 Forcing MathJax re-render...');
        
        // Clear previous rendering
        if (window.MathJax.typesetClear) {
          window.MathJax.typesetClear([mathContainer]);
        }
        
        // Force re-scan and render
        await window.MathJax.typesetPromise([mathContainer]);
        
        console.log('✅ MathJax re-render completed');
      } catch (error) {
        console.error('❌ MathJax re-render failed:', error);
      }
    });
    
    // Wait for re-rendering to complete
    await page.waitForTimeout(2000);
    
    // Check final state
    const finalState = await page.evaluate(() => {
      const mathContainer = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!mathContainer) return { error: 'Math container not found' };
      
      const content = mathContainer.innerHTML;
      return {
        unrenderedInlineMath: (content.match(/\$[^$]+\$/g) || []).length,
        renderedContainers: mathContainer.querySelectorAll('mjx-container').length,
        contentLength: content.length,
        sampleUnrendered: (content.match(/\$[^$]+\$/g) || []).slice(0, 3)
      };
    });
    
    console.log('📊 Final state:', finalState);
    
    // Take screenshot for comparison
    await page.screenshot({ 
      path: 'test-results/math-formula-fixed.png', 
      fullPage: true 
    });
    
    // Verify improvements
    expect(finalState.renderedContainers).toBeGreaterThan(initialState.renderedContainers);
    console.log(`✅ Improved rendering: ${initialState.renderedContainers} → ${finalState.renderedContainers} containers`);
    
    if (finalState.unrenderedInlineMath > 0) {
      console.warn(`⚠️ Still ${finalState.unrenderedInlineMath} unrendered formulas remaining`);
      console.warn('Sample unrendered:', finalState.sampleUnrendered);
    } else {
      console.log('🎉 All math formulas successfully rendered!');
    }
  });

  test('should provide comprehensive math rendering solution', async ({ page }) => {
    console.log('🔬 Starting comprehensive math rendering analysis...');
    
    await page.goto(testUrl);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Comprehensive analysis and fix
    const solution = await page.evaluate(() => {
      const mathContainer = document.querySelector('.markdown-content.prose.max-w-none.tex2jax_process');
      if (!mathContainer) return { error: 'Math container not found' };
      
      const analysis = {
        issues: [],
        fixes: [],
        recommendations: []
      };
      
      let content = mathContainer.innerHTML;
      
      // Issue 1: Check for broken math delimiters
      const brokenDelimiters = content.match(/\$[^$]*<[^>]*>[^$]*\$/g);
      if (brokenDelimiters) {
        analysis.issues.push(`Found ${brokenDelimiters.length} math formulas with HTML tags inside`);
        analysis.fixes.push('Clean HTML tags from math content');
      }
      
      // Issue 2: Check for orphaned LaTeX commands
      const orphanedCommands = content.match(/(?<!\$)\\(?:text|color|xrightarrow|quad|equiv|checkmark|times)\{[^}]*\}(?!\$)/g);
      if (orphanedCommands) {
        analysis.issues.push(`Found ${orphanedCommands.length} orphaned LaTeX commands`);
        analysis.fixes.push('Wrap orphaned LaTeX commands in math delimiters');
      }
      
      // Issue 3: Check for math content split across elements
      const splitMath = content.match(/\$<\/[^>]*>.*?<[^>]*>[^$]*\$/g);
      if (splitMath) {
        analysis.issues.push(`Found ${splitMath.length} math formulas split across HTML elements`);
        analysis.fixes.push('Reconstruct split math formulas');
      }
      
      // Recommendations
      analysis.recommendations = [
        '1. Pre-process content to protect math formulas before Markdown rendering',
        '2. Use placeholder tokens for math content during HTML processing',
        '3. Implement post-processing to restore and clean math formulas',
        '4. Add MathJax configuration to handle edge cases',
        '5. Use tex2jax_ignore class to prevent processing of certain elements'
      ];
      
      return analysis;
    });
    
    console.log('🔍 Comprehensive Analysis:');
    console.log('Issues found:', solution.issues);
    console.log('Recommended fixes:', solution.fixes);
    console.log('Long-term recommendations:', solution.recommendations);
    
    // Verify analysis completed
    expect(solution.issues).toBeDefined();
    expect(solution.fixes).toBeDefined();
    expect(solution.recommendations).toBeDefined();
  });
});
