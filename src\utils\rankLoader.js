/**
 * 🚀 智能排名加载器
 * 功能：异步加载排名数据，支持请求取消和缓存
 */

class RankLoader {
  constructor() {
    this.cache = new Map(); // 排名缓存
    this.pendingRequests = new Map(); // 正在进行的请求
    this.abortControllers = new Map(); // 请求取消控制器
  }

  /**
   * 🎯 获取题目排名（智能缓存 + 请求取消）
   * @param {string} biao - 表名
   * @param {number} id - 题目ID
   * @param {string} allcateid - 分类ID列表
   * @returns {Promise} 排名数据或缓存数据
   */
  async getRank(biao, id, allcateid) {
    const cacheKey = `${biao}_${id}_${allcateid}`;
    
    // 🚀 优化1：检查缓存
    if (this.cache.has(cacheKey)) {
      console.log('🎯 使用缓存排名:', cacheKey);
      return this.cache.get(cacheKey);
    }

    // 🚀 优化2：检查是否已有相同请求在进行
    if (this.pendingRequests.has(cacheKey)) {
      console.log('🔄 等待现有请求:', cacheKey);
      return this.pendingRequests.get(cacheKey);
    }

    // 🚀 优化3：取消之前的请求（如果用户快速切换题目）
    this.cancelPreviousRequests();

    // 🚀 优化4：创建新的请求
    const abortController = new AbortController();
    this.abortControllers.set(cacheKey, abortController);

    const requestPromise = this.fetchRankData(biao, id, allcateid, abortController.signal)
      .then(data => {
        // 请求成功：缓存结果
        this.cache.set(cacheKey, data);
        this.pendingRequests.delete(cacheKey);
        this.abortControllers.delete(cacheKey);
        console.log('✅ 排名加载完成:', cacheKey);
        return data;
      })
      .catch(error => {
        // 请求失败：清理状态
        this.pendingRequests.delete(cacheKey);
        this.abortControllers.delete(cacheKey);
        
        if (error.name === 'AbortError') {
          console.log('🚫 请求已取消:', cacheKey);
          throw new Error('REQUEST_CANCELLED');
        } else {
          console.error('❌ 排名加载失败:', error);
          throw error;
        }
      });

    this.pendingRequests.set(cacheKey, requestPromise);
    return requestPromise;
  }

  /**
   * 🔥 取消之前的所有请求
   */
  cancelPreviousRequests() {
    console.log('🚫 取消之前的请求，数量:', this.abortControllers.size);
    
    for (const [key, controller] of this.abortControllers.entries()) {
      controller.abort();
      console.log('🚫 已取消请求:', key);
    }
    
    // 清理已取消的请求
    this.abortControllers.clear();
    this.pendingRequests.clear();
  }

  /**
   * 📡 实际的网络请求
   */
  async fetchRankData(biao, id, allcateid, signal) {
    const url = `/egg/gettimurank?biao=${biao}&id=${id}&allcateid=${allcateid}`;
    
    console.log('📡 开始请求排名:', url);
    
    const response = await fetch(url, {
      signal, // 支持请求取消
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.res || [];
  }

  /**
   * 🧹 清理缓存（可选，用于内存管理）
   */
  clearCache() {
    console.log('🧹 清理排名缓存');
    this.cache.clear();
  }

  /**
   * 📊 获取缓存统计
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      activeRequests: this.abortControllers.size
    };
  }
}

// 🚀 创建全局单例
const rankLoader = new RankLoader();

export default rankLoader;

/**
 * 🎯 使用示例：
 * 
 * import rankLoader from '@/utils/rankLoader'
 * 
 * // 在题目切换时调用
 * async function loadRankForQuestion(questionId) {
 *   try {
 *     const ranks = await rankLoader.getRank('fbsy', questionId, '656598,801460,801490');
 *     // 更新UI显示排名
 *     updateRankDisplay(ranks);
 *   } catch (error) {
 *     if (error.message === 'REQUEST_CANCELLED') {
 *       console.log('请求被取消，用户切换了题目');
 *     } else {
 *       console.error('加载排名失败:', error);
 *     }
 *   }
 * }
 */
