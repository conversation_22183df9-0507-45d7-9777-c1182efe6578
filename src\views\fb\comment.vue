<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="left">
      <div class="lb" style=""></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <comment v-if="!is404" :uid="uid" :type="type" />
      <div v-else></div>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1" @click="right">
      <div class="rb" style=""></div>
    </a-col>
  </a-row>
</template>
<script setup>
import Comment from '@/components/comment.vue';
import axios from 'axios';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const uid = ref(route.query.id || '5893630');
const type = ref(+route.query.type === 48644 ? 48644 : 6);
const is404 = ref(false);

const checkFbpl = async () => {
  try {
    const params = { id: uid.value, type: type.value === 48644 ? 'xingce' : 'syzc' };
    await axios.get('/egg/fbpl', { params });
    is404.value = false;
  } catch (error) {
    if (error.response && error.response.status === 404) {
      is404.value = true;
    } else {
      is404.value = false;
    }
  }
};

onMounted(checkFbpl);

const left = () => { };
const right = () => { };
</script>
<style>
* body {
  background: #fcf2d7 url('../../../public/bg_paper_mid.jpg');
}
</style>
