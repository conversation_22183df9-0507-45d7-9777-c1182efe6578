# 画布功能工具类

## 概述

`CanvasManager` 是一个专门用于管理画布绘制功能的工具类，提供了完整的画布初始化、绘制、保存、加载等功能。

## 主要功能

### 1. 画布初始化

- 支持桌面端和移动端画布同时初始化
- 自动设置画布尺寸和触摸行为
- 支持数位板笔检测

### 2. 绘制功能

- 鼠标绘制支持
- 触摸绘制支持
- 数位板笔绘制支持（支持压力感应）
- 绘制/擦除模式切换

### 3. 数据管理

- 自动保存画布数据到数据库
- 支持数据压缩和解压
- 防抖保存机制
- 数据恢复功能

### 4. 状态管理

- 完整的画布状态管理
- 状态同步功能
- 资源清理机制

## 使用方法

### 基本使用

```javascript
import { canvasManager } from '@/utils/canvas';

// 初始化画布
canvasManager.initCanvas('drawingCanvas', 'indicatorCanvas', '.wp');

// 设置当前题目ID
canvasManager.setCurrentId('12345');

// 清除画布
await canvasManager.clearCanvas();

// 保存画布数据
await canvasManager.saveCanvasData();

// 加载画布数据
await canvasManager.loadCanvasData();

// 切换绘制模式
await canvasManager.toggleDrawMode();
```

### 在Vue组件中使用

```vue
<template>
  <canvas
    id="drawingCanvas"
    @mousedown="startDrawing"
    @mouseup="stopDrawing"
    @mousemove="handleMouseMove"
    @mouseleave="handleMouseLeave"
    @pointerdown="handlePointerDown"
    @pointermove="handlePointerMove"
    @pointerup="handlePointerUp"
    @pointerleave="handlePointerLeave"
  ></canvas>
</template>

<script setup>
  import { canvasManager } from '@/utils/canvas';

  // 事件处理函数
  const startDrawing = (event) => {
    canvasManager.startDrawing(event);
  };

  const stopDrawing = async () => {
    await canvasManager.stopDrawing();
  };

  const handleMouseMove = (event) => {
    canvasManager.handleMouseMove(event);
    // 同步状态
    const state = canvasManager.getCanvasState();
    mouseX.value = state.mouseX;
    mouseY.value = state.mouseY;
  };

  const handleMouseLeave = () => {
    canvasManager.handleMouseLeave();
  };

  const handlePointerDown = async (event) => {
    const result = await canvasManager.handlePointerDown(event);
    // 同步状态
    const state = canvasManager.getCanvasState();
    isEraseMode.value = state.isEraseMode;
    return result;
  };

  const handlePointerMove = async (event) => {
    const result = await canvasManager.handlePointerMove(event);
    // 同步状态
    const state = canvasManager.getCanvasState();
    mouseX.value = state.mouseX;
    mouseY.value = state.mouseY;
    return result;
  };

  const handlePointerUp = async (event) => {
    await canvasManager.handlePointerUp(event);
    // 同步状态
    const state = canvasManager.getCanvasState();
    isEraseMode.value = state.isEraseMode;
  };

  const handlePointerLeave = async (event) => {
    await canvasManager.handlePointerLeave(event);
  };

  // 生命周期
  onMounted(() => {
    canvasManager.initCanvas();
  });

  onBeforeUnmount(() => {
    canvasManager.cleanup();
  });
</script>
```

## API 参考

### CanvasManager 类

#### 构造函数

```javascript
new CanvasManager();
```

#### 方法

##### initCanvas(canvasId, indicatorCanvasId, containerSelector)

初始化画布

- `canvasId`: 画布元素ID，默认为 'drawingCanvas'
- `indicatorCanvasId`: 指示器画布元素ID，默认为 'indicatorCanvas'
- `containerSelector`: 容器选择器，默认为 '.wp'

##### setCurrentId(id)

设置当前题目ID

- `id`: 题目ID字符串

##### clearCanvas()

清除画布内容

##### saveCanvasData(immediate)

保存画布数据

- `immediate`: 是否立即保存，默认为 false（使用防抖）

##### loadCanvasData()

从数据库加载画布数据

##### toggleDrawMode()

切换绘制/擦除模式

##### startDrawing(event)

开始绘制

- `event`: 鼠标事件对象

##### stopDrawing()

停止绘制

##### draw(event)

绘制

- `event`: 鼠标事件对象

##### handleMouseMove(event)

处理鼠标移动

- `event`: 鼠标事件对象

##### handleMouseLeave()

处理鼠标离开

##### drawEraseIndicator()

绘制擦除范围指示器

##### startDrawingTouch(event)

开始触摸绘制

- `event`: 触摸事件对象

##### drawTouch(event)

触摸绘制

- `event`: 触摸事件对象

##### handlePointerDown(event)

处理指针按下事件

- `event`: 指针事件对象

##### handlePointerMove(event)

处理指针移动事件

- `event`: 指针事件对象

##### handlePointerUp(event)

处理指针抬起事件

- `event`: 指针事件对象

##### handlePointerLeave(event)

处理指针离开事件

- `event`: 指针事件对象

##### getCanvasState()

获取画布状态

##### setCanvasState(state)

设置画布状态

- `state`: 状态对象

##### cleanup()

清理资源

#### 属性

- `drawing`: 是否正在绘制
- `canvasStrokes`: 画布笔画数据
- `canvasSettings`: 画布设置
- `eraseSize`: 擦除范围大小
- `showEraseIndicator`: 是否显示擦除范围指示器
- `mouseX`: 鼠标X坐标
- `mouseY`: 鼠标Y坐标
- `penPressure`: 笔压力值
- `penTiltX`: 笔X轴倾斜
- `penTiltY`: 笔Y轴倾斜
- `penSupported`: 是否支持数位板笔
- `isEraseMode`: 是否处于擦除模式
- `currentId`: 当前题目ID

## 键盘快捷键

- `Ctrl+C`: 切换擦除模式
- `Ctrl+[`: 减小擦除范围
- `Ctrl+]`: 增大擦除范围
- `Ctrl+R`: 切换擦除范围显示

## 注意事项

1. 确保在组件卸载时调用 `cleanup()` 方法清理资源
2. 画布数据会自动保存，但建议在重要操作后手动调用 `saveCanvasData()`
3. 移动端和桌面端画布会同步绘制，确保用户体验一致
4. 数位板笔功能需要浏览器支持 PointerEvent API
5. 擦除范围指示器只在桌面端显示，移动端不显示

## 更新日志

### v1.0.0

- 初始版本
- 支持基本的画布绘制功能
- 支持桌面端和移动端
- 支持数位板笔
- 支持数据保存和加载
