<!-- comment.vue -->
<template>
  <div class="comment-list-container">
    <div v-for="item in data" :key="item.id" class="comment-item">
      <div class="comment-header">
        <span class="nickname" v-html="item.nickName"></span>
        <span class="stars">{{ '⭐'.repeat(item.fiveGradeScore) }}</span>
        <span class="likes">👍 {{ item.likeCount }}</span>
        <span class="timestamp">🗓 {{ formatTime(item.createdTime) }}</span>
        <span class="location">{{ item.ipLocation }}</span>
      </div>

      <div class="comment-body" v-html="item.comment"></div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import dayjs from 'dayjs';
import { defineComponent, nextTick, onMounted, ref, watch } from 'vue';

export default defineComponent({
  name: 'CommentList',
  props: {
    uid: Number,
    type: Number,
  },
  setup(props) {
    const data = ref([]);
    const formatTime = (time) => {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    };

    const initializeComments = async () => {
      if (!props.uid) return;
      await nextTick();
      try {
        const type1 = +props.type === 48644 ? 'xingce' : 'syzc';
        const url = '/egg/fbpl';
        const params = { id: props.uid, type: type1 };
        const response = await axios.get(url, { params });

        // 按点赞数降序排序
        data.value = response?.data?.datas?.sort((a, b) => b.likeCount - a.likeCount);
      } catch (error) {
        console.error('Failed to load comments:', error);
        data.value = [];
      }
    };

    onMounted(initializeComments);

    // 监听 uid 变化，重新加载评论
    watch(
      () => props.uid,
      (newUid, oldUid) => {
        if (newUid !== oldUid) {
          initializeComments();
        }
      },
    );

    return {
      data,
      formatTime,
    };
  },
});
</script>

<style scoped>
.comment-list-container {
  width: 100%;
  font-family: 'LXGW WenKai', serif;
  color: var(--theme-color);
}

.comment-item {
  padding: 1rem 0;
  border-bottom: 1px dashed var(--table-border-color, #e0e0e0);
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* gap: 0.75rem; */ /* 移除 gap，由 ::before 伪元素的 padding 精确控制间距 */
  font-size: 0.875rem; /* 14px */
  color: var(--theme-secondary, #666);
  margin-bottom: 0.5rem; /* 8px */
}

/* 核心修复：为各项之间添加一个优雅的、由 CSS 驱动的分隔符 */
.comment-header > span:not(:first-child)::before {
  content: '||';
  padding: 0 0.5rem; /* 在分隔符两侧添加空间 */
  color: #999; /* 使用一个更深、更中性的灰色，以在两种模式下都提供清晰的视觉分割 */
}

.nickname {
  font-weight: 600;
  color: var(--theme-accent, #1677ee);
}

.stars {
  color: #ffc107;
}

.likes,
.timestamp,
.location {
  display: inline-flex;
  align-items: center;
}

.comment-body {
  font-size: 1.625rem; /* 26px */
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap; /* 保持换行符 */
  color: var(--theme-color);
}

.comment-body :deep(p) {
  margin: 0;
}
</style>
