<script>
  import { defineComponent, ref, onMounted } from 'vue';

  export default defineComponent({
    name: 'ToggleHiddenTag',
    props: {
      messageFromParent: Number,
    },
    setup() {
      const isTagHidden = ref(false);
      const toggleHiddenTag = () => {
        // 使用 document.querySelectorAll 获取所有具有 class="tag" 的元素
        const elements = document.querySelectorAll('.tag');

        // 使用 forEach 方法遍历元素，并根据状态变量切换它们的显示/隐藏状态
        elements.forEach((element) => {
          if (isTagHidden.value) {
            element.style.display = 'block'; // 或者其他适当的显示样式
          } else {
            element.style.display = 'none';
          }
        });

        // 切换状态变量
        isTagHidden.value = !isTagHidden.value;
      };

      onMounted(() => {
        toggleHiddenTag();
      });
      return {
        toggleHiddenTag,
        isTagHidden,
      };
    },
  });
</script>

<template>
  <a-button @click="toggleHiddenTag">隐藏Tag</a-button>
</template>

<style scoped></style>
