import { defineStore } from 'pinia';

export const useThemeStore = defineStore('theme', {
  state: () => ({
    theme: 'plain', // 'plain', 'day', or 'night'
    // 🔧 简化：只保留 sy 页面的主题状态
    syTheme: 'plain', // sy 页面的主题状态
    isSyPage: false, // 当前是否在 sy 页面
  }),
  actions: {
    /**
     * Applies the selected theme by setting CSS variables on the root element.
     * @param {string} mode - The theme mode ('plain', 'day', or 'night').
     */
    _applyTheme(mode) {
      let styles = {};
      switch (mode) {
        case 'plain':
          styles = {
            '--theme-bg': '#ffffff',
            '--theme-color': '#000000',
            '--theme-accent': '#0000ff',
            '--theme-secondary': '#808080',
            '--table-bg': '#ffffff',
            '--table-header-bg': '#f2f2f2',
            '--table-row-alt-bg': '#f9f9f9',
            '--table-border-color': '#e5e5d7',
            '--blockquote-bg': '#f9f9f9',
            '--theme-link-color': '#0000ff',
            '--theme-error-bg': '#fef2f2',
            '--theme-error-text': '#dc2626',
            '--theme-error-border': '#fecaca',
            '--image-bg-color': '#ffffff',
            '--loading-gradient': 'linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%)',
          };
          break;
        case 'night':
          styles = {
            '--theme-bg': '#222222',
            '--theme-color': '#e0e0e0',
            '--theme-accent': '#4fc3f7',
            '--theme-secondary': '#9575cd',
            '--table-bg': '#23272e',
            '--table-header-bg': '#2d313a',
            '--table-row-alt-bg': '#262a32',
            '--table-border-color': '#424242',
            '--blockquote-bg': '#2c2c2c',
            '--theme-link-color': '#60a5fa',
            '--theme-error-bg': '#4a2222',
            '--theme-error-text': '#fda4af',
            '--theme-error-border': '#8f2d2d',
            '--image-bg-color': 'var(--theme-bg)',
            '--loading-gradient': 'linear-gradient(90deg, #3a4668, #7be3e6, #bfc9e6)',
          };
          break;
        case 'day':
        default:
          styles = {
            '--theme-bg': '#f8f5e6',
            '--theme-color': '#333333',
            '--theme-accent': '#1677ee',
            '--theme-secondary': '#b39ddb',
            '--table-bg': '#fffefb',
            '--table-header-bg': '#f3eecb',
            '--table-row-alt-bg': '#f6f3e7',
            '--table-border-color': '#b7b29e',
            '--blockquote-bg': '#e9e8d9',
            '--theme-link-color': '#2563eb',
            '--theme-error-bg': '#fef2f2',
            '--theme-error-text': '#dc2626',
            '--theme-error-border': '#fecaca',
            '--image-bg-color': '#fffefb',
            '--loading-gradient': 'linear-gradient(90deg, #fffbe6, #e0ffd6, #f5f7fa)',
          };
          break;
      }

      for (const [key, value] of Object.entries(styles)) {
        document.documentElement.style.setProperty(key, value);
      }

      // 移除之前的主题类
      document.body.classList.remove('theme-plain', 'theme-day', 'theme-night');
      // 添加当前主题类
      document.body.classList.add(`theme-${mode}`);

      // 确保body样式正确应用
      document.body.style.background = styles['--theme-bg'];
      document.body.style.color = styles['--theme-color'];
    },

    /**
     * 🔧 简化：设置 sy 页面主题
     * @param {string} mode - 主题模式
     */
    setSyTheme(mode) {
      console.log(`🎨 设置 sy 页面主题为: ${mode}`);
      this.syTheme = mode;

      // 只有在 sy 页面时才应用主题
      if (this.isSyPage) {
        this.theme = mode;
        this._applyTheme(mode);
      }

      // 保存 sy 页面主题到 localStorage
      localStorage.setItem('sy-theme', mode);
    },

    /**
     * 🔧 简化：进入 sy 页面
     */
    enterSyPage() {
      console.log('🚪 进入 sy 页面');
      this.isSyPage = true;

      // 恢复 sy 页面的主题
      const savedSyTheme = localStorage.getItem('sy-theme') || 'plain';
      this.syTheme = savedSyTheme;
      this.theme = savedSyTheme;
      this._applyTheme(savedSyTheme);
    },

    /**
     * 🔧 简化：离开 sy 页面，强制白色主题
     */
    leaveSyPage() {
      console.log('🚪 离开 sy 页面，恢复白色主题');
      this.isSyPage = false;
      this.theme = 'plain';
      this._applyTheme('plain');
    },

    /**
     * Sets the theme and persists it to localStorage.
     * @param {string} mode - The theme mode to set.
     * @param {boolean} fromStorageEvent - 是否来自storage事件，避免循环触发
     */
    setTheme(mode, fromStorageEvent = false) {
      this.theme = mode;
      this._applyTheme(mode);

      // 只有不是来自storage事件时才写入localStorage和广播
      if (!fromStorageEvent) {
        localStorage.setItem('theme', mode);
        // 广播主题变化到其他标签页
        this._broadcastThemeChange(mode);
      }
    },

    /**
     * 广播主题变化到其他标签页
     * @param {string} mode - 主题模式
     */
    _broadcastThemeChange(mode) {
      // 使用BroadcastChannel API进行跨标签页通信
      if (typeof BroadcastChannel !== 'undefined') {
        const channel = new BroadcastChannel('theme-sync');
        channel.postMessage({ type: 'THEME_CHANGE', theme: mode });
        channel.close();
      }

      // 备用方案：触发storage事件
      // 通过临时修改localStorage来触发其他标签页的storage事件
      const tempKey = 'theme-broadcast-' + Date.now();
      localStorage.setItem(tempKey, mode);
      localStorage.removeItem(tempKey);
    },

    /**
     * 监听其他标签页的主题变化
     */
    _listenToThemeChanges() {
      // 方案1：使用BroadcastChannel API
      if (typeof BroadcastChannel !== 'undefined') {
        const channel = new BroadcastChannel('theme-sync');
        channel.onmessage = (event) => {
          if (event.data.type === 'THEME_CHANGE') {
            this.setTheme(event.data.theme, true); // fromStorageEvent = true
          }
        };
      }

      // 方案2：监听storage事件（备用方案）
      window.addEventListener('storage', (event) => {
        if (event.key === 'theme') {
          this.setTheme(event.newValue, true); // fromStorageEvent = true
        } else if (event.key && event.key.startsWith('theme-broadcast-')) {
          this.setTheme(event.newValue, true); // fromStorageEvent = true
        }
      });
    },

    /**
     * 🔧 简化：sy 页面主题循环切换
     */
    cycleSyTheme() {
      const themes = ['plain', 'day', 'night'];
      const currentIndex = themes.indexOf(this.syTheme);
      const nextIndex = (currentIndex + 1) % themes.length;
      const newTheme = themes[nextIndex];

      this.setSyTheme(newTheme);
    },

    /**
     * 🔧 简化：初始化主题系统（App.vue 调用）
     */
    initializeTheme() {
      console.log('🎨 初始化主题系统，默认使用白色主题');

      // 初始化为白色主题
      this.theme = 'plain';
      this._applyTheme('plain');

      // 加载 sy 页面保存的主题（但不应用）
      this.syTheme = localStorage.getItem('sy-theme') || 'plain';

      // 开始监听其他标签页的主题变化
      this._listenToThemeChanges();
    },
  },
});
