{"$schema": "https://json.schemastore.org/jsconfig", "name": "vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 6001 -Name Egg && vite --host", "dev:all": "concurrently \"cd ../egg && pnpm dev\" \"pnpm dev\" --names \"🥚Egg,🚀Vue\" --prefix-colors \"cyan,magenta\" --kill-others-on-fail", "build": "vite build", "build:fast": "NODE_ENV=production VITE_BUILD_ANALYZE=false vite build", "build:ultra": "NODE_ENV=production VITE_BUILD_ANALYZE=false VITE_MINIFY=esbuild vite build", "build:analyze": "vite build --mode analyze", "build:profile": "vite build --profile", "preview": "vite preview", "clean": "powershell -ExecutionPolicy Bypass -File ./clean-cache.ps1", "dev:clean": "pnpm clean && pnpm dev", "build:clean": "pnpm clean && pnpm build:fast", "lint": "pnpm exec prettier . --write", "upload": "pnpm build && cd dist && scp -r ./* root@************:/home/<USER>/vue.wcy9.com", "deploy:fast": "pnpm build:fast && cd dist && scp -r ./* root@************:/home/<USER>/vue.wcy9.com", "test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:math": "playwright test tests/math-formula-rendering.spec.js", "test:math:headed": "playwright test tests/math-formula-rendering.spec.js --headed", "test:report": "playwright show-report"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "core-js": "^3.44.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "dplayer": "^1.27.1", "element-plus": "^2.10.4", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-emoji": "^3.0.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-multimd-table": "^4.2.3", "markdown-it-table-of-contents": "^0.9.0", "pinia": "^2.3.1", "qrcode": "^1.5.4", "qweather-icons": "^1.7.0", "socket.io-client": "^2.5.0", "vue": "^3.5.18", "vue-axios": "^3.5.2", "vue-qrcode": "^2.2.2", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@playwright/test": "^1.54.1", "@rollup/plugin-strip": "^3.0.4", "@tailwindcss/postcss": "^4.1.11", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "postcss": "^8.4.47", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.19"}}