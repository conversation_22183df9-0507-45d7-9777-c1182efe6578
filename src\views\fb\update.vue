<!--update.vue-->
<script setup>
  import { onMounted, watch, reactive, nextTick, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { useTimuStore } from '@/store/timu';
  import { storeToRefs } from 'pinia';
  import Shuxue from '@/components/shuxue.vue';
  import axios from 'axios';
  import { message } from 'ant-design-vue';

  const route = useRoute();
  const timuStore = useTimuStore();

  // 使用 storeToRefs 来保持响应性
  const { currentTimuForEdit, isLoading } = storeToRefs(timuStore);

  // 用于强制刷新显示内容的key
  const refreshKey = ref(0);

  // 使用 reactive 来代理 store 中的对象，使其可以直接用 v-model
  const formModel = reactive({
    content: '',
    extra: '',
    answerone: '',
    answertwo: '',
    answerthree: '',
    answerfour: '',
    solution: '',
    material: '',
    materialKeys: '',
    ds: '',
  });

  // 当 aistore.currentTimuForEdit 变化时，更新本地的 formModel
  watch(
    currentTimuForEdit,
    (newTimu) => {
      if (newTimu) {
        formModel.content = newTimu.content;
        formModel.extra = newTimu.extra;
        formModel.answerone = newTimu.answerone;
        formModel.answertwo = newTimu.answertwo;
        formModel.answerthree = newTimu.answerthree;
        formModel.answerfour = newTimu.answerfour;
        formModel.solution = newTimu.solution;
        formModel.material = newTimu.material?.replace(/<\/p>/g, '</p>\\r\\r');
        formModel.materialKeys = newTimu.materialKeys;
        formModel.ds = newTimu.ds;
      }
    },
    { immediate: true, deep: true },
  );

  const postData = async () => {
    const biao = route.query.biao;
    const timuid = route.query.timuid;
    await timuStore.updateTimu({
      biao,
      timuid,
      ...formModel,
    });
  };

  onMounted(async () => {
    document.body.style.background = '#ffffff';
    const biao = route.query.biao;
    const timuid = route.query.timuid;
    await timuStore.fetchTimuForEdit(biao, timuid);
  });

  // OCR功能：从字段内容中提取图片并进行OCR识别
  const performOCR = async (field) => {
    try {
      // 获取对应字段的内容
      let fieldContent = '';
      switch (field) {
        case 'content':
          fieldContent = formModel.content;
          break;
        case 'answerone':
          fieldContent = formModel.answerone;
          break;
        case 'answertwo':
          fieldContent = formModel.answertwo;
          break;
        case 'answerthree':
          fieldContent = formModel.answerthree;
          break;
        case 'answerfour':
          fieldContent = formModel.answerfour;
          break;
        case 'solution':
          fieldContent = formModel.solution;
          break;
        case 'material':
          fieldContent = formModel.material;
          break;
        case 'ds':
          fieldContent = formModel.ds;
          break;
        default:
          message.warning('未知字段');
          return;
      }

      if (!fieldContent) {
        message.warning('字段内容为空');
        return;
      }

      // 使用正则表达式提取第一个img标签的src属性
      const imgSrcRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/i;
      const match = fieldContent.match(imgSrcRegex);

      if (!match || !match[1]) {
        message.warning('未找到图片链接');
        return;
      }

      const imageUrl = match[1];
      // 保存完整的img标签用于后续替换
      const fullImgTag = match[0];
      console.log('提取到的图片链接:', imageUrl);

      // 下载图片
      const imageResponse = await axios.get(imageUrl, {
        responseType: 'blob',
      });

      // 创建FormData并添加图片文件
      const formData = new FormData();
      formData.append('file', imageResponse.data, 'image.png');

      // 发送到OCR接口
      const ocrResponse = await axios.post('/egg/ocr', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (ocrResponse.data && ocrResponse.data.text) {
        // 用OCR识别的文字替换原来的img元素，并用span包裹
        let ocrText = ocrResponse.data.text;

        // 如果OCR文字包含<或>，在前后添加&nbsp;
        ocrText = ocrText.replace(/</g, '&nbsp;<&nbsp;');
        ocrText = ocrText.replace(/>/g, '&nbsp;>&nbsp;');

        const wrappedOcrText = `<span>${ocrText}</span>`;
        const updatedContent = fieldContent.replace(fullImgTag, wrappedOcrText);

        // 将更新后的内容设置回对应字段
        switch (field) {
          case 'content':
            formModel.content = updatedContent;
            break;
          case 'answerone':
            formModel.answerone = updatedContent;
            break;
          case 'answertwo':
            formModel.answertwo = updatedContent;
            break;
          case 'answerthree':
            formModel.answerthree = updatedContent;
            break;
          case 'answerfour':
            formModel.answerfour = updatedContent;
            break;
          case 'solution':
            formModel.solution = updatedContent;
            break;
          case 'material':
            formModel.material = updatedContent;
            break;
          case 'ds':
            formModel.ds = updatedContent;
            break;
        }

        // 强制触发Vue的响应式更新
        refreshKey.value++;
        await nextTick();
        message.success(`OCR识别成功: ${ocrText}`);
      } else {
        message.error('OCR识别失败');
      }
    } catch (error) {
      console.error('OCR处理失败:', error);
      message.error('OCR处理失败: ' + error.message);
    }
  };
</script>

<template>
  <a-row>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="lb" style="" @click=""></div>
    </a-col>
    <a-col :xl="14" :lg="20" :sm="22" :xs="22" :md="22" class="center">
      <div v-if="isLoading">Loading...</div>
      <a-form v-else :model="formModel">
        <a-form-item label="表">
          <a-input v-model:value="route.query.biao" disabled />
        </a-form-item>
        <a-form-item label="题目id">
          <a-input v-model:value="route.query.timuid" disabled />
        </a-form-item>
        <a-form-item label="题目">
          <a-textarea v-model:value="formModel.content" :rows="6" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('content')">OCR识别题目</a-button>
          </div>
          <div v-html="formModel.content" :key="`content-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item label="解析">
          <a-textarea v-model:value="formModel.solution" :rows="6" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('solution')">OCR识别解析</a-button>
          </div>
          <div v-html="formModel.solution" :key="`solution-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item label="额外">
          <a-textarea v-model:value="formModel.extra" :rows="6" />
          <Shuxue :content="formModel.extra" />
        </a-form-item>
        <a-form-item label="材料">
          <a-textarea v-model:value="formModel.material" :rows="6" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('material')">OCR识别材料</a-button>
          </div>
          <div v-html="formModel.material" :key="`material-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
          <a-button type="primary" style="margin-left: 10px" @click="postData">提交</a-button>
        </a-form-item>
        <a-form-item label="选项A">
          <a-textarea v-model:value="formModel.answerone" :rows="3" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('answerone')">OCR识别选项A</a-button>
          </div>
          <div v-html="formModel.answerone" :key="`answerone-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item label="选项B">
          <a-textarea v-model:value="formModel.answertwo" :rows="3" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('answertwo')">OCR识别选项B</a-button>
          </div>
          <div v-html="formModel.answertwo" :key="`answertwo-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item label="选项C">
          <a-textarea v-model:value="formModel.answerthree" :rows="3" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('answerthree')">OCR识别选项C</a-button>
          </div>
          <div v-html="formModel.answerthree" :key="`answerthree-${refreshKey}`"></div>
        </a-form-item>
        <a-form-item label="选项D">
          <a-textarea v-model:value="formModel.answerfour" :rows="3" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('answerfour')">OCR识别选项D</a-button>
          </div>
          <div v-html="formModel.answerfour" :key="`answerfour-${refreshKey}`"></div>
        </a-form-item>

        <a-form-item label="ds">
          <a-textarea v-model:value="formModel.ds" :rows="6" />
          <div style="margin: 8px 0">
            <a-button size="small" @click="performOCR('ds')">OCR识别ds</a-button>
          </div>
          <Shuxue :content="formModel.ds" />
        </a-form-item>
      </a-form>
    </a-col>
    <a-col :xl="5" :lg="2" :sm="1" :xs="1" :md="1">
      <div class="rb" style="" @click=""></div>
    </a-col>
  </a-row>
</template>

<style scoped>
  input,
  textarea {
    background: #ffffff;
  }
</style>
